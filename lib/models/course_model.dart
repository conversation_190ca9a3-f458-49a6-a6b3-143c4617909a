import 'package:wellfedadmin/models/time_duration.dart';

class CourseModel {
  CourseModel({
    required this.docId,
    required this.language,
    required this.imageUrl,
    required this.courseId,
    required this.chapters,
    required this.updatedOn,
    required this.publishedOn,
    required this.tags,
    required this.desc,
    required this.price,
    required this.overview,
    required this.title,
    required this.blocked,
    required this.discountPrice,
    required this.duration,
    required this.author,
    required this.examType,
    required this.bulkMinQty,
    required this.bulkPrice,
    required this.certiValidity,
  });
  late final String docId;
  late final String language;
  late final String imageUrl;
  late final String courseId;
  late final List<Chapters> chapters;
  late final DateTime updatedOn; //
  late final DateTime publishedOn; //
  late final List<String> tags;
  late final String desc;
  late final String overview;
  late final String title;
  late final bool blocked;
  late final TimeDuration duration; //
  late final String author;
  late final String examType;
  late final num price;
  late final num discountPrice;
  late final num bulkPrice;
  late final int bulkMinQty;
  late final int certiValidity;
  late final int days;

  CourseModel.fromJson(String id, Map<String, dynamic> json) {
    docId = id;
    language = json['language'];
    imageUrl = json['imageUrl'];
    courseId = json['courseId'];
    chapters =
        List.from(json['chapters']).map((e) => Chapters.fromJson(e)).toList();
    updatedOn = json['updatedOn'].toDate();
    publishedOn = json['publishedOn'].toDate();
    tags = json['tags'].toString().split(",");
    desc = json['desc'];
    overview = json['overview'];
    title = json['title'];
    blocked = json['blocked'];
    duration =
        TimeDuration(hours: json['duration'][0], minutes: json['duration'][1]);
    author = json['author'];
    examType = json['examType'];
    discountPrice = json['discountPrice'];
    price = json['price'];
    bulkMinQty = json['bulkMinQty'];
    bulkPrice = json['bulkPrice'];
    certiValidity = json['validFor'];
    days = json['days'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['language'] = language;
    data['imageUrl'] = imageUrl;
    data['courseId'] = courseId;
    data['chapters'] = chapters.map((e) => e.toJson()).toList();
    data['updatedOn'] = updatedOn;
    data['publishedOn'] = publishedOn;
    data['tags'] = tags;
    data['desc'] = desc;
    data['price'] = price;
    data['overview'] = overview;
    data['title'] = title;
    data['blocked'] = blocked;
    data['discountPrice'] = discountPrice;
    data['duration'] = duration;
    data['author'] = author;
    data['examType'] = examType;
    data['bulkMinQty'] = bulkMinQty;
    data['bulkPrice'] = bulkPrice;
    data['validFor'] = certiValidity;
    data['days'] = days;
    return data;
  }
}

class Chapters {
  Chapters({
    required this.duration,
    required this.assignments,
    required this.name,
    required this.id,
    required this.modules,
  });
  late final TimeDuration duration;
  late final List<Assignments> assignments;
  late final String name;
  late final String id;
  late final List<Modules> modules;

  Chapters.fromJson(Map<String, dynamic> json) {
    duration =
        TimeDuration(hours: json['duration'][0], minutes: json['duration'][1]);
    assignments = List.from(json['assignments'])
        .map((e) => Assignments.fromJson(e))
        .toList();
    name = json['name'];
    id = json['id'];
    modules =
        List.from(json['modules']).map((e) => Modules.fromJson(e)).toList();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['duration'] = duration;
    data['assignments'] = assignments.map((e) => e.toJson()).toList();
    data['name'] = name;
    data['id'] = id;
    data['modules'] = modules.map((e) => e.toJson()).toList();
    return data;
  }
}

class Assignments {
  Assignments({
    required this.duration,
    required this.minPercentage,
    required this.name,
    required this.id,
    required this.mcqs,
    required this.matches,
  });
  late final TimeDuration duration;
  late final String minPercentage;
  late final String name;
  late final String id;
  late final List<Mcqs> mcqs;
  late final List<Matches> matches;

  Assignments.fromJson(Map<String, dynamic> json) {
    duration =
        TimeDuration(hours: json['duration'][0], minutes: json['duration'][1]);
    minPercentage = json['minPercentage'];
    name = json['name'];
    id = json['id'];
    mcqs = List.from(json['mcqs']).map((e) => Mcqs.fromJson(e)).toList();
    matches =
        List.from(json['matches']).map((e) => Matches.fromJson(e)).toList();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['duration'] = duration;
    data['minPercentage'] = minPercentage;
    data['name'] = name;
    data['id'] = id;
    data['mcqs'] = mcqs;
    data['matches'] = matches.map((e) => e.toJson()).toList();
    return data;
  }
}

class Mcqs {
  Mcqs({
    required this.question,
    required this.answer,
    required this.options,
    required this.id,
  });
  late final String question;
  late final String answer;
  late final List<String> options;
  late final String id;

  Mcqs.fromJson(Map<String, dynamic> json) {
    question = json['question'];
    answer = json['answer'];
    options = List.castFrom<dynamic, String>(json['options']);
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['question'] = question;
    data['answer'] = answer;
    data['options'] = options;
    data['id'] = id;
    return data;
  }
}

class Matches {
  Matches({
    required this.question,
    required this.id,
    required this.pairs,
  });
  late final String question;
  late final String id;
  late final List<Pairs> pairs;

  Matches.fromJson(Map<String, dynamic> json) {
    question = json['question'];
    id = json['id'];
    pairs = List.from(json['pairs']).map((e) => Pairs.fromJson(e)).toList();
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['question'] = question;
    data['id'] = id;
    data['pairs'] = pairs.map((e) => e.toJson()).toList();
    return data;
  }
}

class Pairs {
  Pairs({
    required this.left,
    required this.right,
  });
  late final String left;
  late final String right;

  Pairs.fromJson(Map<String, dynamic> json) {
    left = json['left'];
    right = json['right'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['left'] = left;
    data['right'] = right;
    return data;
  }
}

class Modules {
  Modules({
    required this.duration,
    required this.videoUrl,
    required this.name,
    required this.id,
    required this.content,
    required this.minWatch,
  });
  late final TimeDuration duration;
  late final String videoUrl;
  late final String name;
  late final String id;
  late final String content;
  late final int minWatch;

  Modules.fromJson(Map<String, dynamic> json) {
    duration =
        TimeDuration(hours: json['duration'][0], minutes: json['duration'][1]);
    videoUrl = json['videoUrl'];
    name = json['name'];
    id = json['id'];
    content = json['content'];
    minWatch = json['minWatch'];
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['duration'] = duration;
    data['videoUrl'] = videoUrl;
    data['name'] = name;
    data['id'] = id;
    data['content'] = content;
    data['minWatch'] = minWatch;
    return data;
  }
}



/* import 'package:wellfedadmin/models/time_duration.dart';

class CourseModel {
  final String docId;
  final String title;
  final String desc;
  final TimeDuration duration;
  final DateTime publishedOn;
  final DateTime updatedOn;
  final List<String> tags;
  final String examType;
  final String imageUrl;
  final String author;
  final String language;
  final String courseId;
  final bool blocked;
  final String overview;
  final double price;
  final double discountPrice;
  final List<ChapterModel> chapters;

  CourseModel({
    required this.docId,
    required this.title,
    required this.desc,
    required this.duration,
    required this.publishedOn,
    required this.updatedOn,
    required this.tags,
    required this.examType,
    required this.imageUrl,
    required this.author,
    required this.language,
    required this.courseId,
    required this.blocked,
    required this.overview,
    required this.price,
    required this.discountPrice,
    required this.chapters,
  });
}

class ChapterModel {
  final String id;
  final String name;
  final TimeDuration duration;
  final List<ModuleModel> modules;
  final List<AssignmentModel> assignments;

  ChapterModel({
    required this.id,
    required this.name,
    required this.duration,
    required this.modules,
    required this.assignments,
  });
}

class ModuleModel {
  final String id;
  final String name;
  final TimeDuration duration;
  final String content;
  final String videoUrl;

  ModuleModel({
    required this.id,
    required this.name,
    required this.duration,
    required this.content,
    required this.videoUrl,
  });
}

class AssignmentModel {
  final String id;
  final String name;
  final TimeDuration duration;
  final double minPercentage;
  final List<MCQModel> mcqs;
  final List<MatchModel> matches;

  AssignmentModel({
    required this.id,
    required this.name,
    required this.duration,
    required this.minPercentage,
    required this.mcqs,
    required this.matches,
  });
}

class MCQModel {
  final String id;
  final String question;
  final String answer;
  final List<String> options;

  MCQModel({
    required this.id,
    required this.question,
    required this.answer,
    required this.options,
  });
}

class MatchModel {
  final String id;
  final String question;
  final Map<String, String> pairs;

  MatchModel({
    required this.id,
    required this.question,
    required this.pairs,
  });
}
 */