import 'package:flutter/material.dart';
import 'package:wellfedadmin/models/time_duration.dart';

class CourseInputModel {
  String? docId;
  final TimeDuration duration;
  DateTime? publishedOn;
  DateTime? updatedOn;
  String? courseId;
  bool blocked;
  final List<ChapterInputModel> chapters;
  final GlobalKey<FormState> formKey;
  final TextEditingController titleCtrl;
  final TextEditingController descCtrl;
  final TextEditingController examTypeCtrl;
  final TextEditingController imageUrlCtrl;
  final TextEditingController authorCtrl;
  final TextEditingController languageCtrl;
  final TextEditingController overviewCtrl;
  final TextEditingController priceCtrl;
  final TextEditingController discountPriceCtrl;
  final TextEditingController tagsCtrl;
  final TextEditingController bulkPriceCtrl;
  final TextEditingController bulkMinQtyCtrl;
  final TextEditingController validDaysCtrl;
  final TextEditingController daysCtrl;

  CourseInputModel({
    required this.docId,
    required this.formKey,
    required this.titleCtrl,
    required this.descCtrl,
    required this.duration,
    required this.publishedOn,
    required this.updatedOn,
    required this.tagsCtrl,
    required this.examTypeCtrl,
    required this.imageUrlCtrl,
    required this.authorCtrl,
    required this.languageCtrl,
    required this.courseId,
    required this.blocked,
    required this.overviewCtrl,
    required this.priceCtrl,
    required this.discountPriceCtrl,
    required this.chapters,
    required this.bulkPriceCtrl,
    required this.bulkMinQtyCtrl,
    required this.validDaysCtrl,
    required this.daysCtrl,
  });

  toJsonData() {
    return {
      'duration': [duration.hours, duration.minutes],
      'publishedOn': publishedOn,
      'updatedOn': updatedOn,
      'courseId': courseId,
      'blocked': blocked,
      'chapters': chapters.map((e) => e.toJsonData()),
      'title': titleCtrl.text,
      'desc': descCtrl.text,
      'examType': examTypeCtrl.text,
      'imageUrl': imageUrlCtrl.text,
      'author': authorCtrl.text,
      'language': languageCtrl.text,
      'overview': overviewCtrl.text,
      'tags': tagsCtrl.text,
      'price': num.parse(priceCtrl.text),
      'discountPrice': num.parse(discountPriceCtrl.text),
      'bulkPrice': num.parse(bulkPriceCtrl.text),
      'bulkMinQty': int.parse(bulkMinQtyCtrl.text),
      'validFor': int.parse(validDaysCtrl.text),
      'days': int.parse(daysCtrl.text),
    };
  }
}

class ChapterInputModel {
  final String id;
  final TextEditingController name;
  final TimeDuration duration;
  final List<ModuleInputModel> modules;
  final List<AssignmentInputModel> assignments;

  ChapterInputModel({
    required this.id,
    required this.name,
    required this.duration,
    required this.modules,
    required this.assignments,
  });

  toJsonData() {
    return {
      'id': id,
      'name': name.text,
      'duration': [duration.hours, duration.minutes],
      'modules': modules.map((e) => e.toJsonData()),
      'assignments': assignments.map((e) => e.toJsonData()),
    };
  }
}

class ModuleInputModel {
  final String id;
  final TextEditingController name;
  final TimeDuration duration;
  final TextEditingController content;
  final TextEditingController videoUrl;
  final TextEditingController minWatch;

  ModuleInputModel({
    required this.id,
    required this.name,
    required this.duration,
    required this.content,
    required this.videoUrl,
    required this.minWatch,
  });
  toJsonData() {
    return {
      'id': id,
      'name': name.text,
      'duration': [duration.hours, duration.minutes],
      'content': content.text,
      'videoUrl': videoUrl.text,
      'minWatch': int.parse(minWatch.text),
    };
  }
}

class AssignmentInputModel {
  final String id;
  final TextEditingController name;
  final TimeDuration duration;
  final TextEditingController minPercentage;
  final List<MCQInputModel> mcqs;
  final List<MatchInputModel> matches;

  AssignmentInputModel({
    required this.id,
    required this.name,
    required this.duration,
    required this.minPercentage,
    required this.mcqs,
    required this.matches,
  });
  toJsonData() {
    return {
      'id': id,
      'name': name.text,
      'duration': [duration.hours, duration.minutes],
      'minPercentage': minPercentage.text,
      'mcqs': mcqs.map((e) => e.toJsonData()),
      'matches': matches.map((e) => e.toJsonData()),
    };
  }
}

class MCQInputModel {
  final String id;
  final TextEditingController question;
  int answerIndex;
  final List<TextEditingController> options;

  MCQInputModel({
    required this.id,
    required this.question,
    required this.answerIndex,
    required this.options,
  });
  toJsonData() {
    return {
      'id': id,
      'question': question.text,
      'answer': options[answerIndex].text,
      'options': options.map((e) => e.text).toList(),
    };
  }
}

class MatchInputModel {
  final String id;
  final TextEditingController question;
  final List<MatchPairModel> pairs;

  MatchInputModel({
    required this.id,
    required this.question,
    required this.pairs,
  });

  toJsonData() {
    return {
      'id': id,
      'question': question.text,
      'pairs': pairs.map((e) => e.toJsonData())
    };
  }
}

class MatchPairModel {
  final TextEditingController left;
  final TextEditingController right;

  MatchPairModel({
    required this.left,
    required this.right,
  });
  toJsonData() {
    return {
      'left': left.text,
      'right': right.text,
    };
  }
}
