import 'package:wellfedadmin/models/cart_model.dart';

class UserModel {
  UserModel({
    required this.docId,
    required this.contact,
    required this.email,
    required this.name,
    required this.branch,
    required this.eId,
    required this.cartItems,
    required this.createdBy,
    this.qtyPurchased,
    this.valuePurchased,
  });
  late final String docId;
  late final String contact;
  late final String email;
  late final String name;
  late final String branch;
  late final String? eId;
  late final String? createdBy;
  late final List<CartCourseModel> cartItems;
  late final int? qtyPurchased;
  late final int? valuePurchased;

  UserModel.fromJson(String id, Map<String, dynamic> json) {
    docId = id;
    contact = json['contact'] ?? '';
    email = json['email'] ?? '';
    name = json['name'] ?? '';
    branch = json['branch'] ?? '';
    createdBy = json['createdBy'];
    eId = json['eId'];
    qtyPurchased = json['qtyPurchased'] ?? 0;
    valuePurchased = json['valuePurchased'] ?? 0;

    // Handle cartItems - can be Map, List, or null
    final cartItemsData = json['cartItems'];
    if (cartItemsData == null) {
      cartItems = [];
    } else if (cartItemsData is Map) {
      cartItems = Map.castFrom(cartItemsData)
          .entries
          .map((e) => CartCourseModel.fromJson({e.key: e.value}))
          .toList();
    } else if (cartItemsData is List) {
      cartItems = cartItemsData
          .map((item) =>
              CartCourseModel.fromJson(Map<String, dynamic>.from(item)))
          .toList();
    } else {
      cartItems = [];
    }
  }

  Map<String, dynamic> toJson() {
    final data = <String, dynamic>{};
    data['contact'] = contact;
    data['email'] = email;
    data['createdBy'] = createdBy;
    data['name'] = name;
    data['branch'] = branch;
    data['eId'] = eId;
    data['qtyPurchased'] = qtyPurchased;
    data['valuePurchased'] = valuePurchased;

    // Convert cartItems to Map format for Firebase storage
    final cartItemsMap = <String, dynamic>{};
    for (final item in cartItems) {
      cartItemsMap[item.courseId] = item.qty;
    }
    data['cartItems'] = cartItemsMap;

    return data;
  }
}
