import 'package:cloud_firestore/cloud_firestore.dart';
import 'cart_model.dart';

class OrderModel {
  final String docId;
  final String orderId;

  final String uid;
  final List<CartCourseModel> checkoutCourses;
  final double totalAmount;
  final double discount;
  final bool fromCart;
  final double original;
  final bool processed;
  final DateTime? time;

  OrderModel({
    required this.docId,
    required this.orderId,
    required this.uid,
    required this.checkoutCourses,
    required this.totalAmount,
    required this.discount,
    required this.fromCart,
    required this.original,
    required this.processed,
    required this.time,
  });

  factory OrderModel.fromSnap(DocumentSnapshot snap) {
    final data = snap.data() as Map<String, dynamic>;
    return OrderModel.fromJson(data, docId: snap.id);
  }

  factory OrderModel.fromJson(Map<String, dynamic> json, {String docId = ''}) {
    List<CartCourseModel> courses = [];

    // Handle checkoutCourses - can be List or Map
    final coursesData = json['checkoutCourses'];
    if (coursesData != null) {
      if (coursesData is List) {
        courses = coursesData
            .map((e) => CartCourseModel.fromJson(Map<String, dynamic>.from(e)))
            .toList();
      } else if (coursesData is Map) {
        courses = Map.castFrom(coursesData)
            .entries
            .map((e) => CartCourseModel.fromJson({e.key: e.value}))
            .toList();
      }
    }

    return OrderModel(
      docId: docId.isNotEmpty ? docId : (json['docId'] ?? ''),
      orderId: json['orderId'] ?? '',
      uid: json['uid'] ?? '',
      checkoutCourses: courses,
      totalAmount: (json['totalAmount'] ?? 0).toDouble(),
      discount: (json['discount'] ?? 0).toDouble(),
      fromCart: json['fromCart'] ?? false,
      original: (json['original'] ?? 0).toDouble(),
      processed: json['processed'] ?? false,
      time: json['time'] != null && json['time'] is Timestamp
          ? (json['time'] as Timestamp).toDate()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'orderId': orderId,
      'checkoutCourses': checkoutCourses.map((e) => e.toJson()).toList(),
      'totalAmount': totalAmount,
      'discount': discount,
      'fromCart': fromCart,
      'original': original,
      'processed': processed,
      'time': time,
    };
  }
}
