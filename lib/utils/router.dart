import 'package:go_router/go_router.dart';
import 'package:wellfedadmin/views/auth/auth.dart';
import 'package:wellfedadmin/views/base.dart';
import 'package:wellfedadmin/views/home/<USER>';
import 'package:wellfedadmin/views/users/users_page.dart';
import 'package:wellfedadmin/views/users/user_details_page.dart';

final GoRouter appRouter = GoRouter(
  initialLocation: '/',
  routes: [
    GoRoute(
      path: '/',
      builder: (context, state) => const BaseWid(),
    ),
    GoRoute(
      path: '/login',
      builder: (context, state) => const LoginPage(),
    ),
    GoRoute(
      path: '/home',
      builder: (context, state) => const HomePage(),
    ),
    GoRoute(
      path: '/users',
      builder: (context, state) => const UsersPage(),
    ),
    GoRoute(
      path: '/users/:userId',
      builder: (context, state) {
        final userId = state.pathParameters['userId']!;
        return UserDetailsPage(userId: userId);
      },
    ),
  ],
);
