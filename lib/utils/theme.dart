import 'package:flutter/material.dart';

// Admin Panel Color Scheme
const Color primaryColor = Color(0xff009db8);
const Color primaryVariant = Color(0xff007a8c);
const Color secondaryColor = Color(0xff4caf50);
const Color backgroundColor = Color(0xfff5f5f5);
const Color surfaceColor = Color(0xffffffff);
const Color drawerColor = Color(0xff1e293b);
const Color drawerTextColor = Color(0xffe2e8f0);
const Color drawerSelectedColor = Color(0xff3b82f6);

final materialThemeData = ThemeData(
  useMaterial3: true,
  colorScheme: ColorScheme.fromSeed(
    seedColor: primaryColor,
    brightness: Brightness.light,
    primary: primaryColor,
    secondary: secondaryColor,
    surface: surfaceColor,
  ),
  scaffoldBackgroundColor: backgroundColor,
  appBarTheme: const AppBarTheme(
    backgroundColor: surfaceColor,
    foregroundColor: Colors.black87,
    elevation: 1,
    shadowColor: Colors.black12,
  ),
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: primaryColor,
      foregroundColor: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      minimumSize: const Size(88, 44),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      elevation: 2,
    ).copyWith(
      overlayColor: WidgetStateProperty.resolveWith<Color?>(
        (Set<WidgetState> states) {
          if (states.contains(WidgetState.hovered)) {
            return Colors.white.withOpacity(0.1);
          }
          if (states.contains(WidgetState.pressed)) {
            return Colors.white.withOpacity(0.2);
          }
          return null;
        },
      ),
    ),
  ),
  textButtonTheme: TextButtonThemeData(
    style: TextButton.styleFrom(
      foregroundColor: primaryColor,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      minimumSize: const Size(64, 44),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    ).copyWith(
      overlayColor: WidgetStateProperty.resolveWith<Color?>(
        (Set<WidgetState> states) {
          if (states.contains(WidgetState.hovered)) {
            return primaryColor.withOpacity(0.1);
          }
          if (states.contains(WidgetState.pressed)) {
            return primaryColor.withOpacity(0.2);
          }
          return null;
        },
      ),
    ),
  ),
  outlinedButtonTheme: OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      foregroundColor: primaryColor,
      side: const BorderSide(color: primaryColor),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      minimumSize: const Size(88, 44),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    ).copyWith(
      overlayColor: WidgetStateProperty.resolveWith<Color?>(
        (Set<WidgetState> states) {
          if (states.contains(WidgetState.hovered)) {
            return primaryColor.withOpacity(0.1);
          }
          if (states.contains(WidgetState.pressed)) {
            return primaryColor.withOpacity(0.2);
          }
          return null;
        },
      ),
    ),
  ),
  inputDecorationTheme: InputDecorationTheme(
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: Colors.grey),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: Colors.grey),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(8),
      borderSide: const BorderSide(color: primaryColor, width: 2),
    ),
    contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    filled: true,
    fillColor: surfaceColor,
  ),
  cardTheme: CardThemeData(
    elevation: 2,
    shadowColor: Colors.black12,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
    color: surfaceColor,
  ),
  dividerTheme: const DividerThemeData(
    color: Colors.grey,
    thickness: 1,
    space: 1,
  ),
);

Color getSnackBarColor(SnackBarSeverity severity) {
  switch (severity) {
    case SnackBarSeverity.error:
      return Colors.red.shade100;
    case SnackBarSeverity.warning:
      return Colors.orange.shade100;
    case SnackBarSeverity.success:
      return Colors.green.shade100;
    default:
      return Colors.blue.shade100;
  }
}

enum SnackBarSeverity { error, warning, success, info }
