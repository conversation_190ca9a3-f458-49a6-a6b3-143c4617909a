import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'const.dart';

class ResponsiveWid extends StatelessWidget {
  const ResponsiveWid(
      {Key? key, required this.mobile, this.tablet, this.desktop})
      : super(key: key);
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        if (constraints.maxWidth > desktopMinSize) return desktop ?? mobile;
        if (constraints.maxWidth < desktopMinSize &&
            constraints.maxWidth > mobileMinSize) return tablet ?? mobile;
        return mobile;
      },
    );
  }
}

class ResponsiveWidSmall extends StatelessWidget {
  const ResponsiveWidSmall(
      {Key? key, required this.mobile, this.tablet, this.desktop})
      : super(key: key);
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        if (constraints.maxWidth > desktopMinSize2) return desktop ?? mobile;
        if (constraints.maxWidth < desktopMinSize2 &&
            constraints.maxWidth > mobileMinSize2) return tablet ?? mobile;
        return mobile;
      },
    );
  }
}

class ResponsiveSplitWid extends StatelessWidget {
  const ResponsiveSplitWid(
      {Key? key, required this.split, this.single, this.splitBool})
      : super(key: key);
  final Widget split;
  final Widget? single;
  final RxBool? splitBool;
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        if (constraints.maxWidth < splitViewMinSize) {
          splitBool?.value = false;
          return single ?? split;
        }
        splitBool?.value = true;
        return split;
      },
    );
  }
}
