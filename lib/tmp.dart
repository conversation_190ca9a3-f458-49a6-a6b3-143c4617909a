// class CourseModel {
//     CourseModel({
//       required this.languageCtrl,
//       required this.imageUrlCtrl,
//       required this. courseId,
//       required this. chapters,
//       required this.updatedOn,
//       required this.publishedOn,
//       required this.tagsCtrl,
//       required this.descCtrl,
//       required this.priceCtrl,
//       required this.overviewCtrl,
//       required this.titleCtrl,
//       required this.blocked,
//       required this.discountPriceCtrl,
//       required this.duration,
//       required this.authorCtrl,
//       required this.examTypeCtrl,
//     });
//     late final String languageCtrl;
//     late final String imageUrlCtrl;
//     late final String  courseId;
//     late final List< chapters>  chapters;
//     late final String updatedOn;
//     late final String publishedOn;
//     late final String tagsCtrl;
//     late final String descCtrl;
//     late final String priceCtrl;
//     late final String overviewCtrl;
//     late final String titleCtrl;
//     late final bool blocked;
//     late final String discountPriceCtrl;
//     late final List<String> duration;
//     late final String authorCtrl;
//     late final String examTypeCtrl;
    
//     CourseModel.fromJson(Map<String, dynamic> json){
//       languageCtrl = json['languageCtrl'];
//       imageUrlCtrl = json['imageUrlCtrl'];
//        courseId = json[' courseId'];
//        chapters = List.from(json[' chapters']).map((e)=> chapters.fromJson(e)).toList();
//       updatedOn = json['updatedOn'];
//       publishedOn = json['publishedOn'];
//       tagsCtrl = json['tagsCtrl'];
//       descCtrl = json['descCtrl'];
//       priceCtrl = json['priceCtrl'];
//       overviewCtrl = json['overviewCtrl'];
//       titleCtrl = json['titleCtrl'];
//       blocked = json['blocked'];
//       discountPriceCtrl = json['discountPriceCtrl'];
//       duration = List.castFrom<dynamic, String>(json['duration']);
//       authorCtrl = json['authorCtrl'];
//       examTypeCtrl = json['examTypeCtrl'];
//     }
  
//     Map<String, dynamic> toJson() {
//       final _data = <String, dynamic>{};
//       _data['languageCtrl'] = languageCtrl;
//       _data['imageUrlCtrl'] = imageUrlCtrl;
//       _data[' courseId'] =  courseId;
//       _data[' chapters'] =  chapters.map((e)=>e.toJson()).toList();
//       _data['updatedOn'] = updatedOn;
//       _data['publishedOn'] = publishedOn;
//       _data['tagsCtrl'] = tagsCtrl;
//       _data['descCtrl'] = descCtrl;
//       _data['priceCtrl'] = priceCtrl;
//       _data['overviewCtrl'] = overviewCtrl;
//       _data['titleCtrl'] = titleCtrl;
//       _data['blocked'] = blocked;
//       _data['discountPriceCtrl'] = discountPriceCtrl;
//       _data['duration'] = duration;
//       _data['authorCtrl'] = authorCtrl;
//       _data['examTypeCtrl'] = examTypeCtrl;
//       return _data;
//     }
//   }
  
//   class  chapters {
//      chapters({
//       required this.duration,
//       required this. assignments,
//       required this.name,
//       required this.id,
//       required this.modules,
//     });
//     late final List<String> duration;
//     late final List< assignments>  assignments;
//     late final String name;
//     late final String id;
//     late final List<Modules> modules;
    
//      chapters.fromJson(Map<String, dynamic> json){
//       duration = List.castFrom<dynamic, String>(json['duration']);
//        assignments = List.from(json[' assignments']).map((e)=> assignments.fromJson(e)).toList();
//       name = json['name'];
//       id = json['id'];
//       modules = List.from(json['modules']).map((e)=>Modules.fromJson(e)).toList();
//     }
  
//     Map<String, dynamic> toJson() {
//       final _data = <String, dynamic>{};
//       _data['duration'] = duration;
//       _data[' assignments'] =  assignments.map((e)=>e.toJson()).toList();
//       _data['name'] = name;
//       _data['id'] = id;
//       _data['modules'] = modules.map((e)=>e.toJson()).toList();
//       return _data;
//     }
//   }
  
//   class  assignments {
//      assignments({
//       required this.duration,
//       required this. minPercentage,
//       required this. name,
//       required this.id,
//       required this.mcqs,
//       required this.matches,
//     });
//     late final List<String> duration;
//     late final String  minPercentage;
//     late final String  name;
//     late final String id;
//     late final List<Mcqs> mcqs;
//     late final List<Matches> matches;
    
//      assignments.fromJson(Map<String, dynamic> json){
//       duration = List.castFrom<dynamic, String>(json['duration']);
//        minPercentage = json[' minPercentage'];
//        name = json[' name'];
//       id = json['id'];
//       mcqs = json['mcqs'];
//       matches = List.from(json['matches']).map((e)=>Matches.fromJson(e)).toList();
//     }
  
//     Map<String, dynamic> toJson() {
//       final _data = <String, dynamic>{};
//       _data['duration'] = duration;
//       _data[' minPercentage'] =  minPercentage;
//       _data[' name'] =  name;
//       _data['id'] = id;
//       _data['mcqs'] = mcqs;
//       _data['matches'] = matches.map((e)=>e.toJson()).toList();
//       return _data;
//     }
//   }
  
//   class Mcqs {
//     Mcqs({
//       required this.question,
//       required this. answer,
//       required this.options,
//       required this. id,
//     });
//     late final String question;
//     late final String  answer;
//     late final List<String> options;
//     late final String  id;
    
//     Mcqs.fromJson(Map<String, dynamic> json){
//       question = json['question'];
//        answer = json[' answer'];
//       options = List.castFrom<dynamic, String>(json['options']);
//        id = json[' id'];
//     }
  
//     Map<String, dynamic> toJson() {
//       final _data = <String, dynamic>{};
//       _data['question'] = question;
//       _data[' answer'] =  answer;
//       _data['options'] = options;
//       _data[' id'] =  id;
//       return _data;
//     }
//   }
  
//   class Matches {
//     Matches({
//       required this.question,
//       required this.id,
//       required this.pairs,
//     });
//     late final String question;
//     late final String id;
//     late final List<Pairs> pairs;
    
//     Matches.fromJson(Map<String, dynamic> json){
//       question = json['question'];
//       id = json['id'];
//       pairs = List.from(json['pairs']).map((e)=>Pairs.fromJson(e)).toList();
//     }
  
//     Map<String, dynamic> toJson() {
//       final _data = <String, dynamic>{};
//       _data['question'] = question;
//       _data['id'] = id;
//       _data['pairs'] = pairs.map((e)=>e.toJson()).toList();
//       return _data;
//     }
//   }
  
//   class Pairs {
//     Pairs({
//       required this.left,
//       required this.right,
//     });
//     late final String left;
//     late final String right;
    
//     Pairs.fromJson(Map<String, dynamic> json){
//       left = json['left'];
//       right = json['right'];
//     }
  
//     Map<String, dynamic> toJson() {
//       final _data = <String, dynamic>{};
//       _data['left'] = left;
//       _data['right'] = right;
//       return _data;
//     }
//   }
  
//   class Modules {
//     Modules({
//       required this.duration,
//       required this.videoUrl,
//       required this.name,
//       required this.id,
//       required this.content,
//     });
//     late final List<String> duration;
//     late final String videoUrl;
//     late final String name;
//     late final String id;
//     late final String content;
    
//     Modules.fromJson(Map<String, dynamic> json){
//       duration = List.castFrom<dynamic, String>(json['duration']);
//       videoUrl = json['videoUrl'];
//       name = json['name'];
//       id = json['id'];
//       content = json['content'];
//     }
  
//     Map<String, dynamic> toJson() {
//       final _data = <String, dynamic>{};
//       _data['duration'] = duration;
//       _data['videoUrl'] = videoUrl;
//       _data['name'] = name;
//       _data['id'] = id;
//       _data['content'] = content;
//       return _data;
//     }
//   }