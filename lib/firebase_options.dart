// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyDxcQd3CtNQCPqhfRs1Nc-HCCszJZ4VwHQ',
    appId: '1:721138257333:web:ed2908c05c7507803f5dfc',
    messagingSenderId: '721138257333',
    projectId: 'wellfed-9384f',
    authDomain: 'wellfed-9384f.firebaseapp.com',
    storageBucket: 'wellfed-9384f.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyB2o_DWYsGpyPyfvGnoqx9pm3hKmpfqvpY',
    appId: '1:721138257333:android:b4f5024bfd11657d3f5dfc',
    messagingSenderId: '721138257333',
    projectId: 'wellfed-9384f',
    storageBucket: 'wellfed-9384f.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDJlWCSo89kHQL0ReJ2RzaPHvHsJUEYwag',
    appId: '1:721138257333:ios:9f6f232c88c3cdd33f5dfc',
    messagingSenderId: '721138257333',
    projectId: 'wellfed-9384f',
    storageBucket: 'wellfed-9384f.appspot.com',
    iosClientId: '721138257333-e2qk2n9fgjo9ukrvedkpfknfqblahf1t.apps.googleusercontent.com',
    iosBundleId: 'com.rmedia.wellfedadmin',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDJlWCSo89kHQL0ReJ2RzaPHvHsJUEYwag',
    appId: '1:721138257333:ios:47ed3e2b33e9d2153f5dfc',
    messagingSenderId: '721138257333',
    projectId: 'wellfed-9384f',
    storageBucket: 'wellfed-9384f.appspot.com',
    iosClientId: '721138257333-diphh2onbq3gm2lemfcu3v8unma5cer6.apps.googleusercontent.com',
    iosBundleId: 'com.rmedia.wellfedadmin.RunnerTests',
  );
}
