import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:wellfedadmin/utils/theme.dart';
import 'package:wellfedadmin/utils/router.dart';
import 'firebase_options.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp.router(
      title: "WellFed Admin",
      debugShowCheckedModeBanner: false,
      theme: materialThemeData,
      routerConfig: appRouter,
    );
  }
}
