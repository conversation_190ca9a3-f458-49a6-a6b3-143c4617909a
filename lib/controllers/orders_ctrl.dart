import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfedadmin/models/orders_model.dart';
import 'package:wellfedadmin/models/user_course_model.dart';
import 'package:wellfedadmin/models/user_model.dart';
import 'package:wellfedadmin/utils/firebase.dart';
import 'package:wellfedadmin/utils/methods.dart';
import 'package:wellfedadmin/utils/theme.dart';

class OrdersCtrl extends GetxController {
  final searchCtrl = TextEditingController();

  // Pagination variables
  static const int ordersPerPage = 20;
  int currentPage = 1;
  int totalPages = 1;
  int totalOrders = 0;
  int totalOrdersInPeriod = 0; // Total orders in selected month/date

  List<OrderModel> orders = <OrderModel>[];
  List<OrderModel> filteredOrders = <OrderModel>[];
  Map<String, UserModel> usersCache = <String, UserModel>{};
  Map<String, String> courseIdCache = {};
  bool isLoading = false;
  DocumentSnapshot? lastDocument;
  bool hasMoreData = true;

  Map<String, bool> userIsEnterpriseMap = {};
  Map<String, List<UserCourseModel>> userCoursesMap = {};

  // Month filtering
  DateTime selectedMonth = DateTime.now();
  DateTime? selectedDate;

  int activeOrdersCountVal = 0;
  int expiredOrdersCountVal = 0;

  @override
  void onInit() {
    super.onInit();
    // Set selected month to current month and load orders for current month
    selectedMonth = DateTime(DateTime.now().year, DateTime.now().month);
    _loadTotalOrdersCount(); // Load total orders count first
    loadOrdersForMonth(selectedMonth);
    searchCtrl.addListener(onSearchChanged);
  }

  Future<void> _loadTotalOrdersCount() async {
    try {
      final countSnapshot = await FBFireStore.orders.count().get();
      totalOrders = countSnapshot.count ?? 0;
      update();
    } catch (e) {
      debugPrint('Error getting total orders count: $e');
      totalOrders = 0;
    }
  }

  @override
  void onClose() {
    searchCtrl.removeListener(onSearchChanged);
    searchCtrl.dispose();
    super.onClose();
  }

  int get activeOrdersCount {
    final now = DateTime.now();
    int count = 0;

    for (var order in orders) {
      final userCourses = userCoursesMap[order.uid] ?? [];

      final orderCourseIds =
          order.checkoutCourses.map((e) => e.courseId).toSet();

      final relevantUserCourses = userCourses
          .where((course) => orderCourseIds.contains(course.courseId))
          .toList();

      // If any relevant user course is active
      bool hasActiveCourse = relevantUserCourses.any((userCourse) {
        if (userCourse.endDate == null) return true;
        final endDate = userCourse.endDate!.toDate();
        return endDate.isAfter(now) || endDate.isAtSameMomentAs(now);
      });

      if (hasActiveCourse) {
        count++;
      }
    }

    return count;
  }

  int get expiredOrdersCount {
    final now = DateTime.now();
    int count = 0;

    for (var order in orders) {
      final userCourses = userCoursesMap[order.uid] ?? [];

      final orderCourseIds =
          order.checkoutCourses.map((e) => e.courseId).toSet();

      final relevantUserCourses = userCourses
          .where((course) => orderCourseIds.contains(course.courseId))
          .toList();

      // Consider expired if none are active and at least one relevant course exists
      bool hasActiveCourse = relevantUserCourses.any((userCourse) {
        if (userCourse.endDate == null) return true;
        final endDate = userCourse.endDate!.toDate();
        return endDate.isAfter(now) || endDate.isAtSameMomentAs(now);
      });

      if (relevantUserCourses.isNotEmpty && !hasActiveCourse) {
        count++;
      }
    }

    return count;
  }

  void computeOrderStatusCounts() {
    final now = DateTime.now();
    int activeCount = 0;
    int expiredCount = 0;

    for (var order in orders) {
      final userCourses = userCoursesMap[order.uid] ?? [];
      final orderCourseIds =
          order.checkoutCourses.map((e) => e.courseId).toSet();

      final relevantUserCourses = userCourses
          .where((course) => orderCourseIds.contains(course.courseId))
          .toList();

      bool hasActive = relevantUserCourses.any((userCourse) {
        if (userCourse.endDate == null) return true;
        final endDate = userCourse.endDate!.toDate();
        return endDate.isAfter(now) || endDate.isAtSameMomentAs(now);
      });

      if (hasActive) {
        activeCount++;
      } else if (relevantUserCourses.isNotEmpty) {
        expiredCount++;
      }
    }

    activeOrdersCountVal = activeCount;
    expiredOrdersCountVal = expiredCount;
    update();
  }

  void filterOrdersByDate(DateTime date) {
    isLoading = true;
    update();

    // Filter orders by date - comparing just the date part ignoring time
    filteredOrders = orders.where((order) {
      if (order.time == null) return false;
      final orderDate =
          DateTime(order.time!.year, order.time!.month, order.time!.day);
      final selectedDate = DateTime(date.year, date.month, date.day);
      return orderDate == selectedDate;
    }).toList();

    totalOrders = filteredOrders.length;
    isLoading = false;
    update();
  }

  Future<void> loadOrdersForMonth(DateTime month, {bool refresh = true}) async {
    if (isLoading) return;

    selectedMonth = month;
    update();

    try {
      isLoading = true;
      update();

      if (refresh) {
        currentPage = 1;
        lastDocument = null;
        hasMoreData = true;
        orders.clear();
        filteredOrders.clear();
        selectedMonth = month;
        selectedDate = null; // Clear date selection when month changes
      }

      // Set start and end of the month
      final startOfMonth = DateTime(month.year, month.month, 1);
      final endOfMonth = DateTime(month.year, month.month + 1, 0, 23, 59, 59);

      Query query = FBFireStore.orders
          .where('time',
              isGreaterThanOrEqualTo: Timestamp.fromDate(startOfMonth))
          .where('time', isLessThanOrEqualTo: Timestamp.fromDate(endOfMonth))
          .orderBy('time', descending: true);

      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        orders = snapshot.docs.map((doc) => OrderModel.fromSnap(doc)).toList();
        filteredOrders = List.from(orders);

        // Update pagination and flags appropriately if needed
        totalOrdersInPeriod = filteredOrders.length;
        totalPages = 1;
        hasMoreData = false;

        await _loadUsersForOrders(filteredOrders);
        await loadUserCoursesForOrders(filteredOrders);

        // Load course IDs for orders
        await Future.wait(
            filteredOrders.map((order) => loadCourseIdsForOrder(order)));
      } else {
        orders = [];
        filteredOrders = [];
        hasMoreData = false;
        totalOrdersInPeriod = 0;
        totalPages = 1;
      }

      // Get total orders count from all orders
      if (refresh) {
        try {
          final countSnapshot = await FBFireStore.orders.count().get();
          totalOrders = countSnapshot.count ?? 0;
        } catch (e) {
          debugPrint('Error getting total orders count: $e');
        }
      }

      onSearchChanged(); // apply search on filteredOrders if search text exists
    } catch (e) {
      debugPrint('Error loading orders for month: $e');
    } finally {
      isLoading = false;
      update();
    }
  }

  Future<void> loadOrdersForDate(DateTime date, {bool refresh = true}) async {
    if (isLoading) return;

    try {
      isLoading = true;
      update();

      if (refresh) {
        currentPage = 1;
        lastDocument = null;
        hasMoreData = true;
        orders.clear();
        filteredOrders.clear();
        selectedDate = date;
      }

      // Set start and end of the day
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

      Query query = FBFireStore.orders
          .where('time', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('time', isLessThanOrEqualTo: Timestamp.fromDate(endOfDay))
          .orderBy('time', descending: true);

      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        orders = snapshot.docs.map((doc) => OrderModel.fromSnap(doc)).toList();
        filteredOrders = List.from(orders);

        // Update pagination and flags appropriately if needed
        totalOrdersInPeriod = filteredOrders.length;
        totalPages = 1;
        hasMoreData = false;

        await _loadUsersForOrders(filteredOrders);
        await loadUserCoursesForOrders(filteredOrders);

        // Load course IDs for orders
        await Future.wait(
            filteredOrders.map((order) => loadCourseIdsForOrder(order)));
      } else {
        orders = [];
        filteredOrders = [];
        hasMoreData = false;
        totalOrdersInPeriod = 0;
        totalPages = 1;
      }

      // Get total orders count from all orders
      if (refresh) {
        try {
          final countSnapshot = await FBFireStore.orders.count().get();
          totalOrders = countSnapshot.count ?? 0;
        } catch (e) {
          debugPrint('Error getting total orders count: $e');
        }
      }

      onSearchChanged(); // apply search on filteredOrders if search text exists
    } catch (e) {
      debugPrint('Error loading orders for date: $e');
    } finally {
      isLoading = false;
      update();
    }
  }

  Future<void> loadAllOrders() async {
    try {
      isLoading = true;
      update();

      QuerySnapshot snapshot =
          await FBFireStore.orders.orderBy('time', descending: true).get();

      orders = snapshot.docs.map((doc) => OrderModel.fromSnap(doc)).toList();
      filteredOrders = List.from(orders);
      totalOrders = filteredOrders.length;
    } catch (e) {
      debugPrint('Error loading all orders: $e');
    } finally {
      isLoading = false;
      update();
    }
  }

  void onSearchChanged() {
    final query = searchCtrl.text.toLowerCase().trim();
    if (query.isEmpty) {
      // Reset to show all orders for current filter (month/date)
      filteredOrders = List.from(orders);
    } else {
      // Apply search filter to current orders
      filteredOrders = orders.where((order) {
        final user = usersCache[order.uid];
        final userName = user?.name.toLowerCase() ?? '';
        final userEmail = user?.email.toLowerCase() ?? '';
        final userContact = user?.contact.toLowerCase() ?? '';
        final courseIds = getCourseIdsForOrder(order).join(' ').toLowerCase();

        return userName.contains(query) ||
            userEmail.contains(query) ||
            userContact.contains(query) ||
            courseIds.contains(query) ||
            order.totalAmount.toString().contains(query) ||
            order.uid.toLowerCase().contains(query);
      }).toList();
    }
    update();
  }

  Future<String> fetchCourseIdByDocId(String docId) async {
    if (courseIdCache.containsKey(docId)) {
      return courseIdCache[docId]!;
    }
    final docSnapshot = await FBFireStore.courses.doc(docId).get();
    if (docSnapshot.exists) {
      final data = docSnapshot.data() as Map<String, dynamic>;
      final courseId = data['courseId'] as String? ?? 'Unknown';
      courseIdCache[docId] = courseId;
      return courseId;
    }
    return 'Unknown';
  }

  Future<void> loadCourseIdsForOrder(OrderModel order) async {
    final docIds = order.checkoutCourses.map((e) => e.courseId).toList();
    for (var docId in docIds) {
      await fetchCourseIdByDocId(docId); // caches courseId
    }
  }

  List<String> getCourseIdsForOrder(OrderModel order) {
    final docIds = order.checkoutCourses.map((e) => e.courseId).toList();
    return docIds.map((docId) => courseIdCache[docId] ?? 'Loading').toList();
  }

  Future<void> loadOrders({bool refresh = true}) async {
    if (isLoading) return;

    try {
      isLoading = true;
      update();

      if (refresh) {
        currentPage = 1;
        lastDocument = null;
        hasMoreData = true;
        orders.clear();
        filteredOrders.clear();
      }

      final countSnapshot = await FBFireStore.orders.count().get();
      totalOrders = countSnapshot.count ?? 0;

      print("Total orders from Firestore count: $totalOrders");
      totalPages = (totalOrders / ordersPerPage).ceil();

      Query query = FBFireStore.orders
          .orderBy('time', descending: true)
          .limit(ordersPerPage);
      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        filteredOrders =
            snapshot.docs.map((doc) => OrderModel.fromSnap(doc)).toList();
        lastDocument = snapshot.docs.last;
        hasMoreData = snapshot.docs.length == ordersPerPage;

        await _loadUsersForOrders(filteredOrders);
        await loadUserCoursesForOrders(filteredOrders);

        // <<-- Add this: load courseIds for all orders fetched
        await Future.wait(
            filteredOrders.map((order) => loadCourseIdsForOrder(order)));
      } else {
        hasMoreData = false;
        filteredOrders = [];
      }

      onSearchChanged();
    } catch (e) {
      debugPrint('Error loading orders: $e');
    } finally {
      isLoading = false;
      update();
    }
  }

  Future<void> _loadUsersForOrders(List<OrderModel> ordersList) async {
    final userIds = ordersList.map((order) => order.uid).toSet();

    // Filter out UIDs that are already cached
    final uncachedUserIds =
        userIds.where((uid) => !usersCache.containsKey(uid)).toList();

    if (uncachedUserIds.isEmpty) return;

    try {
      // Load users in batches to avoid too many concurrent requests
      const batchSize = 10;
      for (int i = 0; i < uncachedUserIds.length; i += batchSize) {
        final batch = uncachedUserIds.skip(i).take(batchSize);
        final futures = batch.map((uid) async {
          try {
            final userDoc = await FBFireStore.users.doc(uid).get();
            if (userDoc.exists && userDoc.data() != null) {
              usersCache[uid] = UserModel.fromJson(
                  uid, userDoc.data() as Map<String, dynamic>);
            } else {
              // Create a placeholder user for missing users
              usersCache[uid] = UserModel(
                docId: uid,
                name: 'User Not Found',
                email: '',
                contact: '',
                branch: '',
                eId: null,
                createdBy: null,
                cartItems: [],
              );
            }
          } catch (e) {
            debugPrint('Error loading user $uid: $e');
            // Create a placeholder user for error cases
            usersCache[uid] = UserModel(
              docId: uid,
              name: 'Error Loading User',
              email: '',
              contact: '',
              branch: '',
              eId: null,
              createdBy: null,
              cartItems: [],
            );
          }
        });

        await Future.wait(futures);
      }
    } catch (e) {
      debugPrint('Error in _loadUsersForOrders: $e');
    }
  }

  Future<void> loadPage(int page) async {
    if (page < 1 || page > totalPages || isLoading) return;

    try {
      isLoading = true;
      currentPage = page;
      update();

      // Use proper pagination with startAfter
      Query query = FBFireStore.orders
          .orderBy('time', descending: true)
          .limit(ordersPerPage);

      // For pages beyond the first, we need to get the last document from previous page
      if (page > 1) {
        final previousPageSnapshot = await FBFireStore.orders
            .orderBy('time', descending: true)
            .limit((page - 1) * ordersPerPage)
            .get();

        if (previousPageSnapshot.docs.isNotEmpty) {
          query = query.startAfterDocument(previousPageSnapshot.docs.last);
        }
      }

      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        filteredOrders =
            snapshot.docs.map((doc) => OrderModel.fromSnap(doc)).toList();

        // Load users for current page orders
        await _loadUsersForOrders(filteredOrders);
      } else {
        filteredOrders = [];
      }

      onSearchChanged(); // Apply any existing search filter
    } catch (e) {
      debugPrint('Error loading page: $e');
    } finally {
      isLoading = false;
      update();
    }
  }

  Future<void> deleteOrder(BuildContext context, String orderId) async {
    try {
      await FBFireStore.orders.doc(orderId).delete();

      // Remove from local list
      orders.removeWhere((order) => order.docId == orderId);
      filteredOrders.removeWhere((order) => order.docId == orderId);

      // Update total count
      totalOrders = totalOrders > 0 ? totalOrders - 1 : 0;
      totalPages = (totalOrders / ordersPerPage).ceil();

      // If current page is empty and not the first page, go to previous page
      if (filteredOrders.isEmpty && currentPage > 1) {
        currentPage--;
        await loadPage(currentPage);
      } else {
        update();
      }

      if (context.mounted) {
        showAppDialog(context, "Order deleted successfully!");
      }
    } catch (e) {
      debugPrint('Error deleting order: $e');
      if (context.mounted) {
        showAppDialog(
            context, "Error deleting order: $e", SnackBarSeverity.error);
      }
    }
  }

  Future<void> updateOrder(
      BuildContext context, OrderModel updatedOrder) async {
    try {
      await FBFireStore.orders
          .doc(updatedOrder.docId)
          .update(updatedOrder.toJson());

      // Update local list
      final index =
          orders.indexWhere((order) => order.docId == updatedOrder.docId);
      if (index != -1) {
        orders[index] = updatedOrder;
      }

      final filteredIndex = filteredOrders
          .indexWhere((order) => order.docId == updatedOrder.docId);
      if (filteredIndex != -1) {
        filteredOrders[filteredIndex] = updatedOrder;
      }

      update();

      if (context.mounted) {
        showAppDialog(context, "Order updated successfully!");
      }
    } catch (e) {
      debugPrint('Error updating order: $e');
      if (context.mounted) {
        showAppDialog(
            context, "Error updating order: $e", SnackBarSeverity.error);
      }
    }
  }

  Future<OrderModel?> getOrderById(String orderId) async {
    try {
      final doc = await FBFireStore.orders.doc(orderId).get();
      if (doc.exists) {
        final order = OrderModel.fromSnap(doc);
        // Load user data if not cached
        if (!usersCache.containsKey(order.uid)) {
          await _loadUsersForOrders([order]);
        }
        return order;
      }
    } catch (e) {
      debugPrint('Error getting order by ID: $e');
    }
    return null;
  }

  String getUserName(String uid) {
    final user = usersCache[uid];
    if (user != null && user.name.isNotEmpty && user.name != 'Loading...') {
      return user.name;
    }

    // If user is not cached, try to load it asynchronously
    if (!usersCache.containsKey(uid)) {
      _loadSingleUser(uid);
      return 'Loading...';
    }

    return user?.name ?? 'Unknown User';
  }

  Future<void> _loadSingleUser(String uid) async {
    try {
      final userDoc = await FBFireStore.users.doc(uid).get();
      if (userDoc.exists && userDoc.data() != null) {
        usersCache[uid] =
            UserModel.fromJson(uid, userDoc.data() as Map<String, dynamic>);
        update(); // Trigger UI update when user data is loaded
      }
    } catch (e) {
      debugPrint('Error loading single user $uid: $e');
    }
  }

  Future<void> loadUserCoursesForOrders(List<OrderModel> ordersList) async {
    final userIds = ordersList.map((o) => o.uid).toSet();

    final uncachedUserIds =
        userIds.where((uid) => !userCoursesMap.containsKey(uid)).toList();

    for (var uid in uncachedUserIds) {
      try {
        final querySnapshot =
            await FBFireStore.userCourses.where('uid', isEqualTo: uid).get();

        final userCourses = querySnapshot.docs
            .map((doc) => UserCourseModel.fromJson(doc.id, doc.data()))
            .toList();

        userCoursesMap[uid] = userCourses;

        // Set enterprise flag to true if any course has isEnterprise true
        bool isEnterprise = userCourses.any((course) => course.isEnterprise);
        userIsEnterpriseMap[uid] = isEnterprise;
      } catch (e) {
        debugPrint('Error fetching user courses for $uid: $e');
        userCoursesMap[uid] = [];
        userIsEnterpriseMap[uid] = false;
      }
    }

    update();
    computeOrderStatusCounts();
  }

  String getOrderStatus(OrderModel order) {
    final userCourses = userCoursesMap[order.uid] ?? [];
    if (userCourses.isEmpty) return 'Unknown';

    // Get course IDs from the order
    final orderCourseIds = order.checkoutCourses.map((e) => e.courseId).toSet();

    // Find user courses that match the order's courses
    final relevantUserCourses = userCourses
        .where((userCourse) => orderCourseIds.contains(userCourse.courseId))
        .toList();

    if (relevantUserCourses.isEmpty) return 'Unknown';

    final now = DateTime.now();

    // Check if any of the relevant courses are still active
    bool hasActiveCourse = relevantUserCourses.any((userCourse) {
      if (userCourse.endDate == null) return true; // No end date means active
      return userCourse.endDate!.toDate().isAfter(now) ||
          userCourse.endDate!.toDate().isAtSameMomentAs(now);
    });

    return hasActiveCourse ? 'Active' : 'Expired';
  }

  List<UserCourseModel> getUserCoursesForOrder(OrderModel order) {
    final userCourses = userCoursesMap[order.uid] ?? [];
    final orderCourseIds = order.checkoutCourses.map((e) => e.courseId).toSet();

    return userCourses
        .where((userCourse) => orderCourseIds.contains(userCourse.courseId))
        .toList();
  }

  Future<void> updateUserCourseEndDate(
      BuildContext context, String userCourseId, DateTime newEndDate) async {
    try {
      await FBFireStore.userCourses.doc(userCourseId).update({
        'endDate': Timestamp.fromDate(newEndDate),
      });

      // Update local cache
      for (var userCoursesList in userCoursesMap.values) {
        for (var userCourse in userCoursesList) {
          if (userCourse.docId == userCourseId) {
            userCourse.endDate = Timestamp.fromDate(newEndDate);
            break;
          }
        }
      }

      update();

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('End date updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error updating user course end date: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating end date: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

// Returns all unique months (as DateTime) that have orders, sorted descending
  List<DateTime> get monthsWithOrders {
    final monthSet = <DateTime>{};
    for (var order in orders) {
      if (order.time != null) {
        var dt = DateTime(order.time!.year, order.time!.month);
        monthSet.add(dt);
      }
    }
    var monthsList = monthSet.toList();
    monthsList.sort((a, b) => b.compareTo(a));
    return monthsList;
  }
}
