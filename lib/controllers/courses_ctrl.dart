import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:wellfedadmin/models/course_input_models.dart';
import 'package:wellfedadmin/models/course_model.dart';
import 'package:wellfedadmin/utils/firebase.dart';
import 'package:wellfedadmin/utils/methods.dart';
import 'package:wellfedadmin/utils/theme.dart';
import 'package:wellfedadmin/views/courses/course_detail_view.dart';

class CoursesCtrl extends GetxController {
  final searchCtrl = TextEditingController();
  List<CourseModel> courses = <CourseModel>[];

  // Statistics
  int activeCourses = 0;
  double totalRevenue = 0.0;
  int activeUsers = 0;

  @override
  void onInit() {
    super.onInit();
    setUpCoursesStream();
    loadStatistics();
  }

  Future<void> setUpCoursesStream() async {
    try {
      FBFireStore.courses.snapshots().listen((event) {
        courses = event.docs
            .map((e) => CourseModel.fromJson(e.id, e.data()))
            .toList();
        update();
      });
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> deleteCourse(BuildContext context, String courseId) async {
    try {
      await FBFireStore.courses.doc(courseId).delete();
      if (context.mounted) showAppDialog(context, "Course Deleted!");
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<void> duplicateCourse(BuildContext context, CourseModel course) async {
    try {
      CourseInputModel courseInputModel =
          await compute((message) => isolatedCourseMaker(course), "Message");
      courseInputModel.publishedOn = DateTime.now();
      courseInputModel.updatedOn = DateTime.now();
      courseInputModel.imageUrlCtrl.text = "";
      courseInputModel.courseId =
          'WF${await Get.find<CoursesCtrl>().getIdForCourse()}';
      await FBFireStore.courses.add(courseInputModel.toJsonData());
      if (context.mounted) {
        showAppDialog(context, "Course Duplicated!", SnackBarSeverity.success);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  Future<String?> getIdForCourse() async {
    try {
      final sfDocRef = FBFireStore.data;
      return await FBFireStore.fs.runTransaction<String?>((transaction) async {
        return transaction.get(sfDocRef).then((sfDoc) {
          final newCourseNo = sfDoc.get("courseCount") + 1;
          transaction.update(sfDocRef, {"courseCount": newCourseNo});
          return newCourseNo.toString().padLeft(4, '0');
        });
      }).then((value) => value,
          onError: (e) => debugPrint('Error in fetching course no.: $e'));
    } catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }

  Future<void> loadStatistics() async {
    try {
      await Future.wait([
        _calculateActiveCourses(),
        _calculateTotalRevenue(),
        _calculateActiveUsers(),
      ]);
      update();
    } catch (e) {
      debugPrint('Error loading statistics: $e');
    }
  }

  Future<void> _calculateActiveCourses() async {
    try {
      final snapshot = await FBFireStore.courses
          .where('blocked', isEqualTo: false)
          .count()
          .get();
      activeCourses = snapshot.count ?? 0;
    } catch (e) {
      debugPrint('Error calculating active courses: $e');
      activeCourses = 0;
    }
  }

  Future<void> _calculateTotalRevenue() async {
    try {
      final ordersSnapshot = await FBFireStore.orders.get();
      double revenue = 0.0;

      for (var doc in ordersSnapshot.docs) {
        final data = doc.data();
        final totalAmount = (data['totalAmount'] as num?)?.toDouble() ?? 0.0;
        revenue += totalAmount;
      }

      totalRevenue = revenue;
    } catch (e) {
      debugPrint('Error calculating total revenue: $e');
      totalRevenue = 0.0;
    }
  }

  Future<void> _calculateActiveUsers() async {
    try {
      final userCoursesSnapshot = await FBFireStore.userCourses.get();
      final Set<String> activeUserIds = {};
      final now = DateTime.now();

      for (var doc in userCoursesSnapshot.docs) {
        final data = doc.data();
        final endDate = data['endDate'] as Timestamp?;
        final uid = data['uid'] as String?;

        if (uid != null && endDate != null) {
          final endDateTime = endDate.toDate();
          // User is active if end date is in the future or today
          if (endDateTime.isAfter(now) ||
              (endDateTime.year == now.year &&
                  endDateTime.month == now.month &&
                  endDateTime.day == now.day)) {
            activeUserIds.add(uid);
          }
        }
      }

      activeUsers = activeUserIds.length;
    } catch (e) {
      debugPrint('Error calculating active users: $e');
      activeUsers = 0;
    }
  }
}
