import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfedadmin/models/user_course_model.dart';
import 'package:wellfedadmin/models/user_model.dart';
import 'package:wellfedadmin/utils/firebase.dart';
import 'package:wellfedadmin/utils/methods.dart';
import 'package:wellfedadmin/utils/theme.dart';

class UsersCtrl extends GetxController {
  final searchCtrl = TextEditingController();

  // Pagination variables
  static const int usersPerPage = 7;
  int currentPage = 1;
  int totalPages = 1;
  int totalUsers = 0;

  List<UserModel> users = <UserModel>[];
  List<UserModel> filteredUsers = <UserModel>[];
  bool isLoading = false;
  DocumentSnapshot? lastDocument;
  bool hasMoreData = true;

  Map<String, List<UserCourseModel>> userCoursesMap = {};

  @override
  void onInit() {
    super.onInit();
    loadUsers();
    searchCtrl.addListener(_onSearchChanged);
  }

  @override
  void onClose() {
    searchCtrl.removeListener(_onSearchChanged);
    searchCtrl.dispose();
    super.onClose();
  }

  void _onSearchChanged() {
    if (searchCtrl.text.isEmpty) {
      filteredUsers = users;
    } else {
      final query = searchCtrl.text.toLowerCase();
      filteredUsers = users.where((user) {
        return user.name.toLowerCase().contains(query) ||
            user.email.toLowerCase().contains(query) ||
            user.branch.toLowerCase().contains(query);
      }).toList();
    }
    update();
  }

  Future<void> loadUsers({bool refresh = false}) async {
    if (isLoading) return;

    try {
      isLoading = true;
      update();

      if (refresh) {
        currentPage = 1;
        lastDocument = null;
        hasMoreData = true;
        users.clear();
        userCoursesMap.clear();
      }

      final countSnapshot = await FBFireStore.users.count().get();
      totalUsers = countSnapshot.count ?? 0;
      totalPages = (totalUsers / usersPerPage).ceil();

      Query query = FBFireStore.users.orderBy('name').limit(usersPerPage);

      if (lastDocument != null && !refresh) {
        query = query.startAfterDocument(lastDocument!);
      }

      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        final newUsers = snapshot.docs
            .map((doc) =>
                UserModel.fromJson(doc.id, doc.data() as Map<String, dynamic>))
            .toList();

        if (refresh) {
          users = newUsers;
        } else {
          users.addAll(newUsers);
        }

        lastDocument = snapshot.docs.last;
        hasMoreData = snapshot.docs.length == usersPerPage;

        // Fetch user courses for the loaded users
        await _loadUserCoursesForUsers(newUsers);
      } else {
        hasMoreData = false;
      }

      filteredUsers = users;
      _onSearchChanged(); // Apply any existing search filter
    } catch (e) {
      debugPrint('Error loading users: $e');
    } finally {
      isLoading = false;
      update();
    }
  }

  Future<void> _loadUserCoursesForUsers(List<UserModel> usersList) async {
    for (var user in usersList) {
      try {
        // Query userCourses collection where assignedByUid and assignedByName match user
        final coursesSnapshot = await FBFireStore.userCourses
            .where('uid', isEqualTo: user.docId)
            .get();

        final courses = coursesSnapshot.docs
            .map((doc) => UserCourseModel.fromJson(doc.id, doc.data()))
            .toList();

        userCoursesMap[user.docId] = courses;
        // print('Courses loaded for ${user.name}: ${courses.length}');
      } catch (e) {
        debugPrint('Error loading courses for user ${user.docId}: $e');
        userCoursesMap[user.docId] = [];
      }
    }
    update();
  }

  List<UserCourseModel> getCoursesForUser(String userId) {
    // print('Courses for $userId: ${userCoursesMap[userId]?.length}');
    return userCoursesMap[userId] ?? [];
  }

  Future<void> loadPage(int page) async {
    if (page < 1 || page > totalPages || isLoading) return;

    try {
      isLoading = true;
      currentPage = page;
      update();

      Query query = FBFireStore.users.orderBy('name').limit(usersPerPage);

      if (page > 1) {
        final previousPageSnapshot = await FBFireStore.users
            .orderBy('name')
            .limit((page - 1) * usersPerPage)
            .get();

        if (previousPageSnapshot.docs.isNotEmpty) {
          query = query.startAfterDocument(previousPageSnapshot.docs.last);
        }
      }

      final snapshot = await query.get();

      if (snapshot.docs.isNotEmpty) {
        final pageUsers = snapshot.docs
            .map((doc) =>
                UserModel.fromJson(doc.id, doc.data() as Map<String, dynamic>))
            .toList();

        filteredUsers = pageUsers;

        // Fetch courses for this page's users
        await _loadUserCoursesForUsers(pageUsers);
      } else {
        filteredUsers = [];
      }

      _onSearchChanged(); // Apply any existing search filter
    } catch (e) {
      debugPrint('Error loading page $page: $e');
    } finally {
      isLoading = false;
      update();
    }
  }

  Future<void> deleteUser(BuildContext context, String userId) async {
    try {
      await FBFireStore.users.doc(userId).delete();

      // Remove from local list
      users.removeWhere((user) => user.docId == userId);
      filteredUsers.removeWhere((user) => user.docId == userId);

      // Update total count
      totalUsers = totalUsers > 0 ? totalUsers - 1 : 0;
      totalPages = (totalUsers / usersPerPage).ceil();

      // If current page is empty and not the first page, go to previous page
      if (filteredUsers.isEmpty && currentPage > 1) {
        currentPage--;
        await loadPage(currentPage);
      } else {
        update();
      }

      if (context.mounted) {
        showAppDialog(context, "User deleted successfully!");
      }
    } catch (e) {
      debugPrint('Error deleting user: $e');
      if (context.mounted) {
        showAppDialog(
            context, "Error deleting user: $e", SnackBarSeverity.error);
      }
    }
  }

  Future<void> updateUser(BuildContext context, UserModel updatedUser) async {
    try {
      await FBFireStore.users
          .doc(updatedUser.docId)
          .update(updatedUser.toJson());

      // Update local list
      final index = users.indexWhere((user) => user.docId == updatedUser.docId);
      if (index != -1) {
        users[index] = updatedUser;
      }

      final filteredIndex =
          filteredUsers.indexWhere((user) => user.docId == updatedUser.docId);
      if (filteredIndex != -1) {
        filteredUsers[filteredIndex] = updatedUser;
      }

      update();

      if (context.mounted) {
        showAppDialog(context, "User updated successfully!");
      }
    } catch (e) {
      debugPrint('Error updating user: $e');
      if (context.mounted) {
        showAppDialog(
            context, "Error updating user: $e", SnackBarSeverity.error);
      }
    }
  }

  Future<UserModel?> getUserById(String userId) async {
    try {
      final doc = await FBFireStore.users.doc(userId).get();
      if (doc.exists) {
        return UserModel.fromJson(doc.id, doc.data() as Map<String, dynamic>);
      }
    } catch (e) {
      debugPrint('Error getting user by ID: $e');
    }
    return null;
  }
}
