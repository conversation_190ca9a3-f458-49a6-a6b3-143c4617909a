import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import '../../models/course_model.dart';

class ExamCourseTile extends StatelessWidget {
  const ExamCourseTile({
    super.key,
    required this.course,
  });
  final CourseModel course;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: CachedNetworkImage(
                imageUrl: course.imageUrl,
                fit: BoxFit.cover,
              )),
        ),
        const SizedBox(width: 20),
        Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  course.title,
                  textAlign: TextAlign.start,
                  style: const TextStyle(
                      fontWeight: FontWeight.bold, fontSize: 18),
                ),
                Text(
                  course.desc,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.left,
                ),
                const SizedBox(height: 4),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text.rich(
                      TextSpan(
                          text: '${course.chapters.length} ',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                          children: const [
                            TextSpan(
                                text: "Chapters",
                                style: TextStyle(fontWeight: FontWeight.normal))
                          ]),
                    ),
                  ],
                ),
              ],
            ))
      ],
    );
  }
}
