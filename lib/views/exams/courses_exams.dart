import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:wellfedadmin/controllers/courses_ctrl.dart';
import 'package:wellfedadmin/models/course_model.dart';
import 'package:wellfedadmin/utils/const.dart';
import 'package:wellfedadmin/views/exams/course_tile.dart';
import 'package:wellfedadmin/views/exams/schedules.dart';

class ExamCourses extends StatelessWidget {
  const ExamCourses({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CoursesCtrl>(
      init: Get.find<CoursesCtrl>(),
      builder: (_) {
        final courseList = _.courses
            .where((element) => element.title
                .toLowerCase()
                .contains(_.searchCtrl.text.toLowerCase()))
            .toList();

        final isMobile = MediaQuery.sizeOf(context).width <= mobileMinSize;

        return Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              TextField(
                controller: _.searchCtrl,
                decoration: const InputDecoration(
                  hintText: "Search...",
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.search),
                ),
                onChanged: (value) => _.update(),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: MasonryGridView.count(
                  crossAxisCount: isMobile ? 1 : 3,
                  mainAxisSpacing: 16,
                  crossAxisSpacing: 16,
                  itemCount: courseList.length,
                  itemBuilder: (context, index) {
                    final course = courseList[index];
                    return CourseCard(
                      course: course,
                      onTap: () => Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => SchedulesView(course: course),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class CourseCard extends StatelessWidget {
  final CourseModel course;
  final VoidCallback? onTap;

  const CourseCard({Key? key, required this.course, this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isMobile = MediaQuery.of(context).size.width < 768;

    String formatDuration() {
      final duration = course.duration;
      return '${duration.hours}h ${duration.minutes}m';
    }

    // String formattedDate(DateTime date) {
    //   return '${date.day}/${date.month}/${date.year}';
    // }

    int totalVideos() {
      int count = 0;
      for (var chapter in course.chapters) {
        count += chapter.modules.length;
      }
      return count;
    }

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Padding(
          padding: EdgeInsets.all(isMobile ? 12 : 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Thumbnail Image
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  course.imageUrl,
                  width: double.infinity,
                  height: 140,
                  fit: BoxFit.cover,
                  errorBuilder: (_, __, ___) => Container(
                    width: double.infinity,
                    height: 140,
                    color: Colors.grey[300],
                    child: const Icon(Icons.broken_image, size: 40),
                  ),
                ),
              ),
              const SizedBox(height: 12),

              // UID and Status row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'UID: ${course.courseId}',
                    style: TextStyle(fontSize: 12, color: Colors.grey[700]),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: course.blocked
                          ? Colors.red.withOpacity(0.2)
                          : Colors.green.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      course.blocked ? 'Off' : 'On',
                      style: TextStyle(
                        color: course.blocked ? Colors.red : Colors.green,
                        fontWeight: FontWeight.w600,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Title
              Text(
                course.title,
                style: TextStyle(
                  fontSize: isMobile ? 16 : 18,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 12),

              // Basic Info chips (without description)
              Wrap(
                spacing: 12,
                runSpacing: 8,
                children: [
                  _infoChip(
                      Icons.menu_book, '${course.chapters.length} Chapters'),
                  _infoChip(Icons.video_collection, '${totalVideos()} Videos'),
                  _infoChip(Icons.timer, formatDuration()),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _infoChip(IconData icon, String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: Colors.grey[700]),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 12, color: Colors.black87),
          ),
        ],
      ),
    );
  }
}
