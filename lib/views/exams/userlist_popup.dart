import 'package:flutter/material.dart';
import 'package:wellfedadmin/models/user_model.dart';

class UsersListPopup extends StatelessWidget {
  final String title;
  final List<UserModel> users;
  final Future<void> Function(BuildContext, List<UserModel>) onDownload;

  const UsersListPopup({
    super.key,
    required this.title,
    required this.users,
    required this.onDownload,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 40, vertical: 24),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final maxWidth =
              constraints.maxWidth < 600 ? constraints.maxWidth : 600;
          final maxHeight =
              constraints.maxHeight < 600 ? constraints.maxHeight : 600;

          return SizedBox(
            width: maxWidth.toDouble(),
            height: maxHeight.toDouble(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Padding(
                  padding: const EdgeInsets.fromLTRB(20, 20, 12, 8),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          'Registered Users',
                          // 'Users for $title',
                          style: Theme.of(context).textTheme.titleLarge,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.download),
                        tooltip: 'Download PDF',
                        onPressed: () => onDownload(context, users),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                    ],
                  ),
                ),
                const Divider(height: 1),
                // Content - users list
                Expanded(
                  child: Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    child: users.isEmpty
                        ? const Center(child: Text('No users found.'))
                        : Scrollbar(
                            // adds a scrollbar for better UX if content overflows
                            child: ListView.separated(
                              itemCount: users.length,
                              separatorBuilder: (_, __) =>
                                  const Divider(height: 1),
                              itemBuilder: (context, index) {
                                final user = users[index];
                                return ListTile(
                                  dense: true,
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 16),
                                  title: Text(user.name,
                                      overflow: TextOverflow.ellipsis),
                                  subtitle: Text(user.email,
                                      overflow: TextOverflow.ellipsis),
                                  leading: CircleAvatar(
                                    child: Text(user.name.isNotEmpty
                                        ? user.name[0].toUpperCase()
                                        : '?'),
                                  ),
                                );
                              },
                            ),
                          ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
