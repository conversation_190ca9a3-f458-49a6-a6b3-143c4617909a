// ignore_for_file: use_build_context_synchronously

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:wellfedadmin/models/schedule_model.dart';
import 'package:wellfedadmin/models/user_model.dart';
import 'package:wellfedadmin/utils/const.dart';
import 'package:wellfedadmin/utils/firebase.dart';
import 'package:wellfedadmin/utils/responsive.dart';
import 'package:wellfedadmin/views/exams/userlist_popup.dart';
import 'package:wellfedadmin/widgets/loader.dart';
import '../../models/course_model.dart';

List<ScheduleModel> toSchList(QuerySnapshot<Map<String, dynamic>> event) =>
    event.docs
        .map(
          (e) => ScheduleModel(
            docId: e.id,
            title: e['title'],
            courseId: e['courseId'],
            date: e['date'].toDate(),
            startTime: e['startTime'].toDate(),
            endTime: e['endTime'].toDate(),
            desc: e['desc'],
          ),
        )
        .toList();

class SchedulesView extends StatefulWidget {
  const SchedulesView({super.key, required this.course});
  final CourseModel course;

  @override
  State<SchedulesView> createState() => _SchedulesViewState();
}

class _SchedulesViewState extends State<SchedulesView> {
  late DateTime start;
  DateTime? end;

  @override
  void initState() {
    super.initState();
    final now = DateTime.now();
    start = DateTime(now.year, now.month, now.day);
  }

  Future<void> generateAndSaveUsersPdf(
      BuildContext context, List<UserModel> users, String title) async {
    final pdf = pw.Document();

    final headers = ['Name', 'Email'];
    final data = users.map((user) => [user.name, user.email]).toList();

    pdf.addPage(
      pw.Page(
        margin: const pw.EdgeInsets.all(24),
        build: (context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text('Users List for $title',
                  style: pw.TextStyle(
                      fontSize: 20, fontWeight: pw.FontWeight.bold)),
              pw.SizedBox(height: 20),
              pw.Table.fromTextArray(
                headers: headers,
                data: data,
                headerStyle: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold, color: PdfColors.white),
                headerDecoration: pw.BoxDecoration(color: PdfColors.blue),
                cellPadding:
                    const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 6),
                cellAlignment: pw.Alignment.centerLeft,
                columnWidths: {
                  0: const pw.FlexColumnWidth(3),
                  1: const pw.FlexColumnWidth(4),
                },
              )
            ],
          );
        },
      ),
    );

    try {
      final pdfBytes = await pdf.save();
      await Printing.sharePdf(
          bytes: pdfBytes,
          filename: 'users_list_${title.replaceAll(' ', '_')}.pdf');
    } catch (e) {
      debugPrint(e.toString());
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error generating PDF: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _appBar(),
      body: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Wrap(
              spacing: 10,
              runSpacing: 10,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Container(
                        margin: EdgeInsets.symmetric(horizontal: 5),
                        child: Material(
                          color: appColorOne,
                          elevation: 2,
                          borderRadius: BorderRadius.circular(16),
                          child: InkWell(
                            borderRadius: BorderRadius.circular(16),
                            onTap: () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate: start,
                                firstDate: DateTime(2020),
                                lastDate: DateTime(2030),
                              );
                              if (date != null) setState(() => start = date);
                            },
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: 18, horizontal: 14),
                              child: Row(
                                children: [
                                  CircleAvatar(
                                    backgroundColor:
                                        Theme.of(context).colorScheme.primary,
                                    radius: 18,
                                    child: Icon(Icons.calendar_today,
                                        color: Colors.white, size: 18),
                                  ),
                                  SizedBox(width: 14),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Start Date',
                                          style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.white)),
                                      SizedBox(height: 5),
                                      Text(
                                          DateFormat('d MMM yyyy')
                                              .format(start),
                                          style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                              color: Colors.white)),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Container(
                        margin: EdgeInsets.symmetric(horizontal: 5),
                        child: Material(
                          color: appColorOne,
                          elevation: 2,
                          borderRadius: BorderRadius.circular(16),
                          child: InkWell(
                            borderRadius: BorderRadius.circular(16),
                            onTap: () async {
                              final date = await showDatePicker(
                                context: context,
                                initialDate: end ?? DateTime.now(),
                                firstDate: DateTime(2020),
                                lastDate: DateTime(2030),
                              );
                              if (date != null) setState(() => end = date);
                            },
                            child: Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: 18, horizontal: 14),
                              child: Row(
                                children: [
                                  CircleAvatar(
                                    backgroundColor:
                                        Theme.of(context).colorScheme.primary,
                                    radius: 18,
                                    child: Icon(Icons.event,
                                        color: Colors.white, size: 21),
                                  ),
                                  SizedBox(width: 14),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('End Date',
                                          style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.white)),
                                      SizedBox(height: 5),
                                      Text(
                                          end == null
                                              ? 'Select'
                                              : DateFormat('d MMM yyyy')
                                                  .format(end!),
                                          style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                              fontSize: 16,
                                              color: Colors.white)),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 20),
            Expanded(
              child: SingleChildScrollView(
                child: StreamBuilder<List<ScheduleModel>>(
                    stream: FBFireStore.schedules
                        .where('courseId', isEqualTo: widget.course.docId)
                        .where('date', isGreaterThan: start, isLessThan: end)
                        .snapshots()
                        .map(toSchList),
                    builder: (context, snapshot) {
                      if (snapshot.hasError) {
                        debugPrint(snapshot.error.toString());
                        return Text(snapshot.error.toString());
                      }
                      if (snapshot.hasData) {
                        return ResponsiveWid(
                          mobile: _Content(
                              gridCount: 1,
                              list: snapshot.data ?? [],
                              generateAndSaveUsersPdf: generateAndSaveUsersPdf),
                          tablet: _Content(
                              gridCount: 2,
                              list: snapshot.data ?? [],
                              generateAndSaveUsersPdf: generateAndSaveUsersPdf),
                          desktop: _Content(
                              gridCount: 2,
                              list: snapshot.data ?? [],
                              generateAndSaveUsersPdf: generateAndSaveUsersPdf),
                        );
                      }
                      return Center(child: loaderWaveDots(color: appColorOne));
                    }),
              ),
            ),
          ],
        ),
      ),
    );
  }

  AppBar _appBar() {
    return AppBar(title: Text(widget.course.title), actions: [
      ElevatedButton(
        onPressed: () => _addNew(null),
        child: const Text("Add New"),
      ),
      const SizedBox(width: 16),
    ]);
  }

  Future<void> _addNew(ScheduleModel? sch) async {
    final titleCtrl = TextEditingController(text: sch?.title);
    final descCtrl = TextEditingController(text: sch?.desc);
    DateTime? selectedDate = sch?.date;
    TimeOfDay? selectedStart =
        sch?.startTime != null ? TimeOfDay.fromDateTime(sch!.startTime) : null;
    TimeOfDay? selectedEnd =
        sch?.endTime != null ? TimeOfDay.fromDateTime(sch!.endTime) : null;
    try {
      await showDialog<String>(
        context: context,
        builder: (context) => StatefulBuilder(builder: (context, setState2) {
          return AlertDialog(
            title: const Text("Add/Edit Schedule"),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: titleCtrl,
                    decoration: const InputDecoration(
                      labelText: "Title/Header",
                    ),
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    title: const Text("Pick a date"),
                    subtitle: Text(selectedDate?.toString().split(' ')[0] ??
                        'No date selected'),
                    trailing: const Icon(Icons.calendar_today),
                    onTap: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: selectedDate ?? DateTime.now(),
                        firstDate: DateTime.now(),
                        lastDate: DateTime.now().add(const Duration(days: 365)),
                      );
                      if (date != null) {
                        setState2(() => selectedDate = date);
                      }
                    },
                  ),
                  ListTile(
                    title: const Text("Start Time"),
                    subtitle: Text(
                        selectedStart?.format(context) ?? 'No time selected'),
                    trailing: const Icon(Icons.access_time),
                    onTap: () async {
                      final time = await showTimePicker(
                        context: context,
                        initialTime: selectedStart ?? TimeOfDay.now(),
                      );
                      if (time != null) {
                        setState2(() => selectedStart = time);
                      }
                    },
                  ),
                  ListTile(
                    title: const Text("End Time"),
                    subtitle: Text(
                        selectedEnd?.format(context) ?? 'No time selected'),
                    trailing: const Icon(Icons.access_time),
                    onTap: () async {
                      final time = await showTimePicker(
                        context: context,
                        initialTime: selectedEnd ?? TimeOfDay.now(),
                      );
                      if (time != null) {
                        setState2(() => selectedEnd = time);
                      }
                    },
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: descCtrl,
                    decoration: const InputDecoration(
                      labelText: "Note/Desc",
                    ),
                    maxLines: 4,
                    minLines: 2,
                  )
                ],
              ),
            ),
            actions: [
              TextButton(
                child: const Text('Cancel'),
                onPressed: () => Navigator.pop(context),
              ),
              ElevatedButton(
                child: const Text('Confirm'),
                onPressed: () async {
                  Navigator.pop(context);
                  // Convert TimeOfDay back to DateTime for storage
                  DateTime? startDateTime;
                  DateTime? endDateTime;
                  if (selectedDate != null && selectedStart != null) {
                    startDateTime = DateTime(
                      selectedDate!.year,
                      selectedDate!.month,
                      selectedDate!.day,
                      selectedStart!.hour,
                      selectedStart!.minute,
                    );
                  }
                  if (selectedDate != null && selectedEnd != null) {
                    endDateTime = DateTime(
                      selectedDate!.year,
                      selectedDate!.month,
                      selectedDate!.day,
                      selectedEnd!.hour,
                      selectedEnd!.minute,
                    );
                  }

                  Map<String, dynamic> data = {
                    "title": titleCtrl.text,
                    "courseId": widget.course.docId,
                    "date": selectedDate,
                    "startTime": startDateTime,
                    "endTime": endDateTime,
                    "desc": descCtrl.text,
                  };
                  sch != null
                      ? await FBFireStore.schedules.doc(sch.docId).update(data)
                      : await FBFireStore.schedules.add(data);
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text("Schedule saved successfully!")),
                    );
                  }
                },
              ),
            ],
          );
        }),
      );
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}

class _Content extends StatelessWidget {
  const _Content(
      {required this.gridCount,
      required this.list,
      required this.generateAndSaveUsersPdf});
  final int gridCount;
  final List<ScheduleModel> list;
  final Future<void> Function(BuildContext, List<UserModel>, String)
      generateAndSaveUsersPdf;

  Future<List<UserModel>> fetchUsersForSchedule(String scheduleDocId) async {
    print('Fetching schedule for $scheduleDocId');
    final scheduledSnapshot = await FBFireStore.scheduled
        .where('schDocId', isEqualTo: scheduleDocId)
        .get();
    print('Scheduled found: ${scheduledSnapshot.docs.length}');

    final userIds =
        scheduledSnapshot.docs.map((doc) => doc['uid'] as String).toList();
    print('User IDs: $userIds');

    if (userIds.isEmpty) return [];

    List<UserModel> users = [];
    const batchSize = 10;

    for (var i = 0; i < userIds.length; i += batchSize) {
      final batch = userIds.sublist(
          i, i + batchSize > userIds.length ? userIds.length : i + batchSize);
      print('Fetching user batch: $batch');

      final userSnapshot = await FBFireStore.users
          .where(FieldPath.documentId, whereIn: batch)
          .get();

      final batchUsers = userSnapshot.docs
          .map((doc) => UserModel.fromJson(doc.id, doc.data()))
          .toList();
      users.addAll(batchUsers);
      print('Fetched users in batch: ${batchUsers.length}');
    }
    print('Total users fetched: ${users.length}');
    return users;
  }

  void showUsersListPopupWithLoader(
      BuildContext context, UsersListPopup popup) async {
    // Show a loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (ctx) => const Center(
        child: CircularProgressIndicator(),
      ),
    );
    await Future.delayed(const Duration(milliseconds: 200)); // adjust as needed

    // Remove the loading dialog
    Navigator.of(context, rootNavigator: true).pop();

    // Now show the real users list popup
    showDialog(
      context: context,
      builder: (_) => popup,
    );
  }

  @override
  Widget build(BuildContext context) {
    return StaggeredGrid.count(
      crossAxisCount: gridCount,
      mainAxisSpacing: 20,
      crossAxisSpacing: 20,
      axisDirection: AxisDirection.down,
      children: list.map((e) {
        return StaggeredGridTile.fit(
          crossAxisCellCount: 1, // each tile occupies exactly 1 column
          child: Card(
            elevation: 3,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: InkWell(
              borderRadius: BorderRadius.circular(12),
              onTap: () async {
                showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (ctx) =>
                      const Center(child: CircularProgressIndicator()),
                );

                List<UserModel> users = [];
                try {
                  users = await fetchUsersForSchedule(e.docId);
                  Navigator.of(context, rootNavigator: true)
                      .pop(); // remove loader
                  showDialog(
                    context: context,
                    builder: (_) => UsersListPopup(
                      title: e.title,
                      users: users,
                      onDownload: (ctx, users) =>
                          generateAndSaveUsersPdf(ctx, users, e.title),
                    ),
                  );
                } catch (e) {
                  Navigator.of(context, rootNavigator: true)
                      .pop(); // remove loader
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error loading users: $e')),
                  );
                }

                // List<UserModel> users = await fetchUsersForSchedule(
                //     e.docId); // your function to load users
                // showUsersListPopupWithLoader(
                //   context,
                //   UsersListPopup(
                //     title: e.title,
                //     users: users,
                //     onDownload: (ctx, users) =>
                //         generateAndSaveUsersPdf(ctx, users, e.title),
                //   ),
                // );

                // showDialog(
                //   context: context,
                //   builder: (context) => UsersListPopup(
                //     title: e.title,
                //     users: users,
                //     onDownload: (ctx, users) =>
                //         generateAndSaveUsersPdf(ctx, users, e.title),
                //   ),
                // );
              },
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            e.title,
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(fontWeight: FontWeight.bold),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          if (e.desc.isNotEmpty) ...[
                            const SizedBox(height: 6),
                            Text(
                              e.desc,
                              style: Theme.of(context).textTheme.bodyMedium,
                              maxLines: 3,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    SizedBox(
                      width: 130,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              const Icon(Icons.calendar_today,
                                  size: 16, color: Colors.grey),
                              const SizedBox(width: 6),
                              Text(
                                DateFormat('d MMM yyyy').format(e.date),
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                          const SizedBox(height: 10),
                          Row(
                            children: [
                              const Icon(Icons.access_time,
                                  size: 16, color: Colors.grey),
                              const SizedBox(width: 6),
                              Text(
                                e.startTime
                                    .toString()
                                    .split(" ")
                                    .last
                                    .split(":")
                                    .sublist(0, 2)
                                    .join(':'),
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                              const Text(" - "),
                              Text(
                                e.endTime
                                    .toString()
                                    .split(" ")
                                    .last
                                    .split(":")
                                    .sublist(0, 2)
                                    .join(':'),
                                style: Theme.of(context).textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList(),
    );
  }
}

//card on tap pop-up
