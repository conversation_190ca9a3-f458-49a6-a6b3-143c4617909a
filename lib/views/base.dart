import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:wellfedadmin/views/auth/auth.dart';
import 'package:wellfedadmin/views/home/<USER>';
import '../widgets/loader.dart';

class BaseWid extends StatefulWidget {
  const BaseWid({super.key});

  @override
  State<BaseWid> createState() => _BaseWidState();
}

class _BaseWidState extends State<BaseWid> {
  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      initialData: FirebaseAuth.instance.currentUser,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(child: loaderDrop());
        }
        if (!snapshot.hasData) {
          return const LoginPage();
        }
        if (snapshot.hasData) {
          return const HomePage();
        }
        return Center(child: loaderDrop());
      },
    );
  }
}
