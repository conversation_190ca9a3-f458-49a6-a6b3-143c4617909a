import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wellfedadmin/models/course_input_models.dart';

class ModuleTileExpander extends StatelessWidget {
  const ModuleTileExpander({
    super.key,
    required this.count,
    required this.index,
    required this.idx,
    required this.module,
    required this.removeModule,
    required this.setState,
    required this.isExpanded,
    required this.onToggle,
  });
  final int count;
  final int index;
  final int idx;
  final ModuleInputModel module;
  final Function setState;
  final Function removeModule;
  final bool isExpanded;
  final VoidCallback onToggle;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Card(
        color: Colors.grey[100],
        child: ExpansionTile(
          onExpansionChanged: (bool isOpen) {
            onToggle();
          },
          initiallyExpanded: isExpanded,
          trailing: IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => removeModule(index, idx),
          ),
          title: Text(
            "Module ${idx + 1}",
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  StaggeredGrid.count(
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                    crossAxisCount: count == 1 ? 1 : count - 1,
                    children: [
                      TextFormField(
                        controller: module.name,
                        decoration: const InputDecoration(
                          labelText: "Title",
                          hintText: "Module Name",
                        ),
                        validator: (value) =>
                            value!.isEmpty ? "Required" : null,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              decoration:
                                  const InputDecoration(labelText: "Hours"),
                              initialValue: module.duration.hours.toString(),
                              keyboardType: TextInputType.number,
                              onChanged: (value) => setState(() => module
                                  .duration.hours = int.tryParse(value) ?? 0),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              decoration:
                                  const InputDecoration(labelText: "Minutes"),
                              initialValue: module.duration.minutes.toString(),
                              keyboardType: TextInputType.number,
                              onChanged: (value) => setState(() => module
                                  .duration.minutes = int.tryParse(value) ?? 0),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 16),
                      TextFormField(
                        controller: module.content,
                        decoration: const InputDecoration(
                          labelText: "Description",
                          hintText: "Readable Content",
                        ),
                        validator: (value) =>
                            value!.isEmpty ? "Required" : null,
                        maxLines: 3,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: module.videoUrl,
                              decoration: const InputDecoration(
                                labelText: "Video Link",
                                hintText: "Playable video link",
                              ),
                              validator: (value) =>
                                  value!.isEmpty ? "Required" : null,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              controller: module.minWatch,
                              decoration: const InputDecoration(
                                labelText: "Min Watch Duration (s)",
                                hintText: "In Seconds",
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) => (value!.isEmpty ||
                                      int.tryParse(value) == null)
                                  ? "Required & Numeric"
                                  : null,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
