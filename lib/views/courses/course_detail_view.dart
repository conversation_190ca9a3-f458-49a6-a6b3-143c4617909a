import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:wellfedadmin/controllers/courses_ctrl.dart';
import 'package:wellfedadmin/models/course_model.dart';
import 'package:wellfedadmin/models/time_duration.dart';
import 'package:wellfedadmin/services/image_picker.dart';
import 'package:wellfedadmin/utils/const.dart';
import 'package:wellfedadmin/utils/firebase.dart';
import 'package:wellfedadmin/utils/methods.dart';
import 'package:wellfedadmin/utils/responsive.dart';
import 'package:wellfedadmin/utils/theme.dart';
import 'package:wellfedadmin/views/courses/chapter_tile.dart';
import 'package:wellfedadmin/widgets/loader.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import '../../models/course_input_models.dart';

Future<CourseInputModel> isolatedCourseMaker(CourseModel? course) async {
  debugPrint("Parcing Course Data...");

  return CourseInputModel(
      docId: course?.docId,
      formKey: GlobalKey<FormState>(),
      titleCtrl: TextEditingController(text: course?.title),
      descCtrl: TextEditingController(text: course?.desc),
      duration: TimeDuration(
          hours: course?.duration.hours ?? 0,
          minutes: course?.duration.minutes ?? 0),
      publishedOn: course?.publishedOn,
      updatedOn: course?.updatedOn,
      tagsCtrl: TextEditingController(text: course?.tags.join(",")),
      examTypeCtrl:
          TextEditingController(text: course?.examType ?? ExamType.online),
      imageUrlCtrl: TextEditingController(text: course?.imageUrl),
      authorCtrl: TextEditingController(text: course?.author),
      languageCtrl: TextEditingController(text: course?.language),
      courseId: course?.courseId,
      blocked: course?.blocked ?? false,
      overviewCtrl: TextEditingController(text: course?.overview),
      priceCtrl: TextEditingController(text: course?.price.toString()),
      discountPriceCtrl:
          TextEditingController(text: course?.discountPrice.toString()),
      bulkMinQtyCtrl:
          TextEditingController(text: course?.bulkMinQty.toString()),
      bulkPriceCtrl: TextEditingController(text: course?.bulkPrice.toString()),
      validDaysCtrl:
          TextEditingController(text: course?.certiValidity.toString()),
      daysCtrl: TextEditingController(text: course?.days.toString()),
      chapters: course?.chapters
              .map(
                (e) => ChapterInputModel(
                  id: e.id,
                  name: TextEditingController(text: e.name),
                  duration: e.duration,
                  modules: e.modules
                      .map((e) => ModuleInputModel(
                          id: e.id,
                          name: TextEditingController(text: e.name),
                          duration: e.duration,
                          content: TextEditingController(text: e.content),
                          videoUrl: TextEditingController(text: e.videoUrl),
                          minWatch: TextEditingController(
                              text: e.minWatch.toString())))
                      .toList(),
                  assignments: e.assignments
                      .map((e) => AssignmentInputModel(
                          id: e.id,
                          name: TextEditingController(text: e.name),
                          duration: e.duration,
                          minPercentage:
                              TextEditingController(text: e.minPercentage),
                          mcqs: e.mcqs
                              .map((e) => MCQInputModel(
                                  id: e.id,
                                  question:
                                      TextEditingController(text: e.question),
                                  answerIndex: e.options.indexOf(e.answer),
                                  options: e.options
                                      .map(
                                          (e) => TextEditingController(text: e))
                                      .toList()))
                              .toList(),
                          matches: e.matches
                              .map((e) => MatchInputModel(
                                  id: e.id,
                                  question:
                                      TextEditingController(text: e.question),
                                  pairs: e.pairs
                                      .map((e) => MatchPairModel(
                                          left:
                                              TextEditingController(text: e.left),
                                          right: TextEditingController(text: e.right)))
                                      .toList()))
                              .toList()))
                      .toList(),
                ),
              )
              .toList() ??
          []);
}

class CourseView extends StatelessWidget {
  const CourseView({super.key, this.courseDocId});
  final String? courseDocId;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CoursesCtrl>(
      init: Get.find<CoursesCtrl>(),
      builder: (_) {
        final course = _.courses
            .firstWhereOrNull((element) => element.docId == courseDocId);
        return CourseViewData(course: course);
      },
    );
  }
}

class CourseViewData extends StatefulWidget {
  const CourseViewData({super.key, this.course});
  final CourseModel? course;

  @override
  State<CourseViewData> createState() => _CourseViewDataState();
}

class _CourseViewDataState extends State<CourseViewData> {
  bool loading = false;
  RxBool uploading = false.obs;
  CourseInputModel? courseInputModel;
  SelectedImage? imageFile;
  int selectedTile = -1;

  @override
  void initState() {
    super.initState();
    // courseInputModel = _setCourseData(widget.course);
    courseDataParser();
  }

  Future<void> courseDataParser() async {
    await Future.delayed(const Duration(milliseconds: 200));
    if (mounted) setState(() => loading = true);
    courseInputModel = await compute(
        (message) => isolatedCourseMaker(widget.course), "Message");
    if (mounted) setState(() => loading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _appBar(),
      body: courseInputModel == null
          ? const Center(child: Text("Loading..."))
          : SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Form(
                key: courseInputModel!.formKey,
                child: ResponsiveWid(
                  mobile: _inputGrid(1),
                  tablet: _inputGrid(2),
                  desktop: _inputGrid(3),
                ),
              ),
            ),
    );
  }

  Widget _inputGrid(int count) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        StaggeredGrid.count(
          crossAxisCount: count,
          mainAxisSpacing: 20,
          crossAxisSpacing: 20,
          children: [
            TextFormField(
              controller: courseInputModel!.titleCtrl,
              decoration: const InputDecoration(labelText: "Title"),
              validator: (value) => value!.isEmpty ? "Required" : null,
            ),
            TextFormField(
              controller: courseInputModel!.descCtrl,
              decoration: const InputDecoration(labelText: "Description"),
              validator: (value) => value!.isEmpty ? "Required" : null,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text("Course Image",
                    style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                SizedBox(
                    height: 60,
                    child: Obx(() => ListTile(
                          leading: courseInputModel
                                      ?.imageUrlCtrl.text.isNotEmpty ??
                                  false
                              ? SizedBox(
                                  width:
                                      56, // fixed width to constrain image size
                                  height: 56, // fixed height
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(8),
                                    child: CachedNetworkImage(
                                      imageUrl:
                                          courseInputModel!.imageUrlCtrl.text,
                                      fit: BoxFit.cover,
                                      errorWidget: (context, url, error) =>
                                          const Icon(Icons.image),
                                    ),
                                  ),
                                )
                              : null,
                          tileColor: Colors.white,
                          onTap: () async {
                            uploading.value = true;
                            final result = await ImagePickerService()
                                .pickImageAndCrop(context);
                            if (result != null) {
                              imageFile = result;
                              _uploadImageFile();
                            }
                            uploading.value = false;
                          },
                          title: const Text(
                            "Select Image",
                            style: TextStyle(fontSize: 12),
                          ),
                          trailing: uploading.value
                              ? loaderWaveDots(color: appColorOne)
                              : courseInputModel!.imageUrlCtrl.text.isNotEmpty
                                  ? const Icon(Icons.check_circle,
                                      color: Colors.grey)
                                  : const Icon(Icons.error, color: Colors.red),
                        ))),
              ],
            ),
            TextFormField(
              controller: courseInputModel!.daysCtrl,
              decoration: const InputDecoration(labelText: "Days to learn"),
              validator: (value) =>
                  value!.isEmpty || int.tryParse(value.toString()) == null
                      ? "Required  & Numbers only"
                      : null,
            ),
            TextFormField(
              controller: courseInputModel!.authorCtrl,
              decoration: const InputDecoration(labelText: "Author"),
              validator: (value) => value!.isEmpty ? "Required" : null,
            ),
            TextFormField(
              controller: courseInputModel!.languageCtrl,
              decoration: const InputDecoration(labelText: "Language"),
              validator: (value) => value!.isEmpty ? "Required" : null,
            ),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: courseInputModel!.priceCtrl,
                    decoration: const InputDecoration(labelText: "Price"),
                    validator: (value) =>
                        value!.isEmpty || num.tryParse(value.toString()) == null
                            ? "Required & Numbers only"
                            : null,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: courseInputModel!.discountPriceCtrl,
                    decoration:
                        const InputDecoration(labelText: "Discount Price"),
                    validator: (value) =>
                        value!.isEmpty || num.tryParse(value.toString()) == null
                            ? "Required  & Numbers only"
                            : null,
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: courseInputModel!.bulkPriceCtrl,
                    decoration: const InputDecoration(
                        labelText: "Bulk Price of 1 qty."),
                    validator: (value) =>
                        value!.isEmpty || num.tryParse(value.toString()) == null
                            ? "Required  & Numbers only"
                            : null,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: courseInputModel!.bulkMinQtyCtrl,
                    decoration:
                        const InputDecoration(labelText: "Bulk Min Qty."),
                    validator: (value) =>
                        value!.isEmpty || int.tryParse(value.toString()) == null
                            ? "Required  & Numbers only"
                            : null,
                  ),
                ),
              ],
            ),
            TextFormField(
              controller: courseInputModel!.validDaysCtrl,
              decoration:
                  const InputDecoration(labelText: "Certi Validity Days"),
              validator: (value) =>
                  value!.isEmpty || int.tryParse(value.toString()) == null
                      ? "Required  & Numbers only"
                      : null,
            ),
            TextFormField(
              controller: courseInputModel!.tagsCtrl,
              decoration: const InputDecoration(
                labelText: "Tags",
                hintText: 'Seperated by ","',
              ),
              validator: (value) => value!.isEmpty ? "Required" : null,
            ),
            TextFormField(
              controller: courseInputModel!.overviewCtrl,
              decoration: const InputDecoration(labelText: "Overview"),
              minLines: 5,
              maxLines: 8,
              validator: (value) => value!.isEmpty ? "Required" : null,
            ),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(labelText: "Hours"),
                    initialValue: courseInputModel!.duration.hours.toString(),
                    keyboardType: TextInputType.number,
                    onChanged: (value) => setState(() => courseInputModel!
                        .duration.hours = int.tryParse(value) ?? 0),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(labelText: "Minutes"),
                    initialValue: courseInputModel!.duration.minutes.toString(),
                    keyboardType: TextInputType.number,
                    onChanged: (value) => setState(() => courseInputModel!
                        .duration.minutes = int.tryParse(value) ?? 0),
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(right: 8.0, left: 2),
              child: Row(
                children: [
                  const Text("Exam Type: "),
                  const SizedBox(width: 8),
                  Radio<String>(
                    value: ExamType.offline,
                    groupValue: courseInputModel!.examTypeCtrl.text,
                    onChanged: (value) => setState(() => courseInputModel!
                        .examTypeCtrl.text = value ?? ExamType.offline),
                  ),
                  Text(ExamType.offline),
                  const SizedBox(width: 8),
                  Radio<String>(
                    value: ExamType.online,
                    groupValue: courseInputModel!.examTypeCtrl.text,
                    onChanged: (value) => setState(() => courseInputModel!
                        .examTypeCtrl.text = value ?? ExamType.online),
                  ),
                  Text(ExamType.online),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 20),
        ElevatedButton(
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.add),
              SizedBox(width: 4),
              Text("Add Chapter"),
            ],
          ),
          onPressed: () {
            courseInputModel!.chapters.add(ChapterInputModel(
                id: getRandomId(courseContentIdLen),
                name: TextEditingController(),
                duration: TimeDuration(hours: 0, minutes: 0),
                modules: [],
                assignments: []));
            setState(() {});
            showAppDialog(context, "Chapter added!", SnackBarSeverity.success);
          },
        ),
        const SizedBox(height: 20),
        ...List.generate(
            courseInputModel!.chapters.length,
            (index) => ChapterTileExpander(
                  selectedTile: selectedTile,
                  onTileChange: (idx) => setState(() => selectedTile = idx),
                  count: count,
                  index: index,
                  chapter: courseInputModel!.chapters[index],
                  setState: setState,
                  removeChapter: _onRemoveChapter,
                  removeModule: _onRemoveModule,
                  removeAssign: _onRemoveAssign,
                  removeAssignQuestion: _onRemoveAssignQuestion,
                ))
      ],
    );
  }

  AppBar _appBar() {
    return AppBar(
      title: Text(
        widget.course == null ? "New Course" : "Course Details",
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      actions: [
        Padding(
          padding: const EdgeInsets.only(right: 12),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (courseInputModel != null) ...[
                Switch(
                  value: !courseInputModel!.blocked,
                  onChanged: (bool value) =>
                      setState(() => courseInputModel!.blocked = !value),
                ),
                Text(
                  !courseInputModel!.blocked ? 'Available' : 'Not Available',
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(width: 16),
              ],
              ElevatedButton(
                onPressed: _onPublish,
                child: loading ? loaderWaveDots() : const Text("Publish"),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _onPublish() async {
    try {
      if (loading) return;
      if (courseInputModel?.imageUrlCtrl.text.isEmpty ?? true) {
        showAppDialog(context, "Please, select image!");
        return;
      }
      if (courseInputModel!.formKey.currentState!.validate()) {
        setState(() => loading = true);
        if (widget.course == null) {
          courseInputModel!.publishedOn = DateTime.now();
          courseInputModel!.updatedOn = DateTime.now();
          courseInputModel!.courseId =
              'WF${await Get.find<CoursesCtrl>().getIdForCourse()}';
        }
        widget.course == null
            ? await FBFireStore.courses.add(courseInputModel!.toJsonData())
            : await FBFireStore.courses
                .doc(widget.course?.docId)
                .update(courseInputModel!.toJsonData());
        setState(() => loading = false);
        if (mounted) {
          showAppDialog(context,
              "Course ${widget.course == null ? 'Published' : 'Updated'}");
        }
      } else {
        showAppDialog(context, "Incomplete data", SnackBarSeverity.warning);
      }
    } catch (e) {
      debugPrint(e.toString());
      setState(() => loading = false);
      showAppDialog(context, "Something went wrong!");
    }
  }

  Future<void> _onRemoveChapter(int index) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete chapter?'),
        content: const Text(
          'If you delete this chapter, you won\'t be able to recover it after publishing. Do you want to delete it?',
        ),
        actions: [
          TextButton(
            child: const Text('Delete'),
            onPressed: () {
              Navigator.pop(context, true);
            },
          ),
          ElevatedButton(
            child: const Text('Cancel'),
            onPressed: () => Navigator.pop(context, false),
          ),
        ],
      ),
    );
    if (result == true) {
      courseInputModel!.chapters.removeAt(index);
      setState(() {});
    }
  }

  Future<void> _onRemoveModule(int index, int idx) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Module?'),
        content: const Text(
          'If you delete this module, you won\'t be able to recover it after publishing. Do you want to delete it?',
        ),
        actions: [
          TextButton(
            child: const Text('Delete'),
            onPressed: () {
              Navigator.pop(context, true);
            },
          ),
          ElevatedButton(
            child: const Text('Cancel'),
            onPressed: () => Navigator.pop(context, false),
          ),
        ],
      ),
    );
    if (result == true) {
      courseInputModel!.chapters[index].modules.removeAt(idx);
      setState(() {});
    }
  }

  Future<void> _onRemoveAssign(int index, int idx) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Assignment?'),
        content: const Text(
          'If you delete this assignment, you won\'t be able to recover it after publishing. Do you want to delete it?',
        ),
        actions: [
          TextButton(
            child: const Text('Delete'),
            onPressed: () {
              Navigator.pop(context, true);
            },
          ),
          ElevatedButton(
            child: const Text('Cancel'),
            onPressed: () => Navigator.pop(context, false),
          ),
        ],
      ),
    );
    if (result == true) {
      courseInputModel!.chapters[index].assignments.removeAt(idx);
      setState(() {});
    }
  }

  Future<void> _onRemoveAssignQuestion(
      bool isMcq, int chapIndex, int assignIndex, int qIndex) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Question?'),
        content: const Text(
          'If you delete this question, you won\'t be able to recover it after publishing. Do you want to delete it?',
        ),
        actions: [
          TextButton(
            child: const Text('Delete'),
            onPressed: () {
              Navigator.pop(context, true);
            },
          ),
          ElevatedButton(
            child: const Text('Cancel'),
            onPressed: () => Navigator.pop(context, false),
          ),
        ],
      ),
    );
    if (result == true) {
      isMcq
          ? courseInputModel!.chapters[chapIndex].assignments[assignIndex].mcqs
              .removeAt(qIndex)
          : courseInputModel!
              .chapters[chapIndex].assignments[assignIndex].matches
              .removeAt(qIndex);
      setState(() {});
    }
  }

  void _uploadImageFile() async {
    try {
      if (imageFile == null) return;
      uploading.value = true;
      final imageRef = FBStorage.courseFiles.child(
          '${DateTime.now().millisecondsSinceEpoch}.${imageFile!.extention}');
      await imageRef.putData(imageFile!.uInt8List).whenComplete(() async {
        if (courseInputModel!.imageUrlCtrl.text.isNotEmpty) {
          try {
            await FBStorage.fb
                .refFromURL(courseInputModel!.imageUrlCtrl.text)
                .delete();
          } catch (e) {
            debugPrint(e.toString());
          }
        }
        final newUrl = await imageRef.getDownloadURL();
        courseInputModel!.imageUrlCtrl.text = newUrl;
        if (widget.course != null) {
          await FBFireStore.courses
              .doc(widget.course!.docId)
              .update({'imageUrl': newUrl});
        }
        uploading.value = false;
      });
      if (context.mounted) {
        showAppDialog(context, "Image Uploaded", SnackBarSeverity.success);
      }
    } on Exception catch (e) {
      debugPrint(e.toString());
      uploading.value = false;
      showAppDialog(context, "Cannot upload image!", SnackBarSeverity.error);
    }
  }
}

class ExamType {
  static String offline = examTypes[0];
  static String online = examTypes[1];
}

/* StreamBuilder(
            stream: FBFireStore.courses.snapshots().map((event) => null),
            builder: (BuildContext context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return Center(child: loaderCircular());
              }
              if (snapshot.hasError) {
                return const Center(child: Text("Something went wrong!"));
              }
              if (snapshot.hasData) {
                return CourseViewData(course: snapshot.data);
              }
              return Center(child: loaderCircular());
            },
          ); */
          
  /*  CourseInputModel _setCourseData(CourseModel? course) {
    return CourseInputModel(
        docId: course?.docId,
        formKey: GlobalKey<FormState>(),
        titleCtrl: TextEditingController(text: course?.title),
        descCtrl: TextEditingController(text: course?.desc),
        duration: TimeDuration(
            hours: course?.duration.hours ?? 0,
            minutes: course?.duration.hours ?? 0),
        publishedOn: course?.publishedOn,
        updatedOn: course?.updatedOn,
        tagsCtrl: TextEditingController(text: course?.tags.join(",")),
        examTypeCtrl: TextEditingController(text: ExamType.online),
        imageUrlCtrl: TextEditingController(text: course?.imageUrl),
        authorCtrl: TextEditingController(text: course?.author),
        languageCtrl: TextEditingController(text: course?.language),
        courseId: course?.courseId,
        blocked: course?.blocked ?? false,
        overviewCtrl: TextEditingController(text: course?.overview),
        priceCtrl: TextEditingController(text: course?.price.toString()),
        discountPriceCtrl:
            TextEditingController(text: course?.discountPrice.toString()),
        chapters: course?.chapters
                .map(
                  (e) => ChapterInputModel(
                    id: e.id,
                    name: TextEditingController(text: e.name),
                    duration: TimeDuration(
                        hours: e.duration.hours, minutes: e.duration.minutes),
                    modules: e.modules
                        .map((e) => ModuleInputModel(
                            id: e.id,
                            name: TextEditingController(text: e.name),
                            duration: e.duration,
                            content: TextEditingController(text: e.content),
                            videoUrl: TextEditingController(text: e.videoUrl)))
                        .toList(),
                    assignments: e.assignments
                        .map((e) => AssignmentInputModel(
                            id: e.id,
                            name: TextEditingController(text: e.name),
                            duration: e.duration,
                            minPercentage:
                                TextEditingController(text: e.minPercentage),
                            mcqs: e.mcqs
                                .map((e) => MCQInputModel(
                                    id: e.id,
                                    question:
                                        TextEditingController(text: e.question),
                                    answerIndex: e.options.indexOf(e.answer),
                                    options: e.options
                                        .map((e) =>
                                            TextEditingController(text: e))
                                        .toList()))
                                .toList(),
                            matches: e.matches
                                .map((e) => MatchInputModel(
                                    id: e.id,
                                    question:
                                        TextEditingController(text: e.question),
                                    pairs: e.pairs
                                        .map((e) => MatchPairModel(
                                            left: TextEditingController(text: e.left),
                                            right: TextEditingController(text: e.right)))
                                        .toList()))
                                .toList()))
                        .toList(),
                  ),
                )
                .toList() ??
            []);
  }
 */