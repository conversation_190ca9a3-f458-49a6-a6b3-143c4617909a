import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wellfedadmin/models/course_input_models.dart';

class MatchTileExpander extends StatelessWidget {
  const MatchTileExpander(
      {super.key,
      required this.count,
      required this.index,
      required this.idx,
      required this.i,
      required this.match,
      required this.removeAssignQuestion,
      required this.setState});
  final int count;
  final int index;
  final int idx;
  final int i;
  final MatchInputModel match;
  final Function setState;
  final Function removeAssignQuestion;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Card(
        color: Colors.grey[100],
        child: ExpansionTile(
          initiallyExpanded: true,
          trailing: IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => removeAssignQuestion(false, index, idx, i),
          ),
          title: Text(
            "Match ${i + 1}",
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  StaggeredGrid.count(
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                    crossAxisCount: count == 1 ? 1 : count - 1,
                    children: [
                      StaggeredGridTile.fit(
                        crossAxisCellCount: 2,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: match.question,
                                decoration: const InputDecoration(
                                  labelText: "Question",
                                  hintText: "What is the question?",
                                ),
                                validator: (value) =>
                                    value!.isEmpty ? "Required" : null,
                              ),
                            ),
                            const SizedBox(width: 8),
                            ElevatedButton(
                              child: const Text("Add Pair"),
                              onPressed: () => setState(() => match.pairs.add(
                                  MatchPairModel(
                                      left: TextEditingController(),
                                      right: TextEditingController()))),
                            ),
                          ],
                        ),
                      ),
                      ...List.generate(
                          match.pairs.length,
                          (ii) => StaggeredGridTile.fit(
                                crossAxisCellCount: 2,
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Expanded(
                                      child: TextFormField(
                                        controller: match.pairs[ii].left,
                                        decoration: InputDecoration(
                                          hintText: "Key ${ii + 1}",
                                        ),
                                        validator: (value) =>
                                            value!.isEmpty ? "Required" : null,
                                      ),
                                    ),
                                    const Icon(Icons.arrow_forward),
                                    Expanded(
                                      child: TextFormField(
                                        controller: match.pairs[ii].right,
                                        decoration: InputDecoration(
                                          hintText: "Value ${ii + 1}",
                                        ),
                                        validator: (value) =>
                                            value!.isEmpty ? "Required" : null,
                                      ),
                                    ),
                                    IconButton(
                                      icon: const Icon(Icons.delete),
                                      onPressed: () => setState(
                                          () => match.pairs.removeAt(ii)),
                                    )
                                  ],
                                ),
                              ))
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
