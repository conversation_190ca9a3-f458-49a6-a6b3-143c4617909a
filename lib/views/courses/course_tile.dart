import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfedadmin/controllers/courses_ctrl.dart';
import '../../models/course_model.dart';

class CourseTile extends StatelessWidget {
  const CourseTile({
    super.key,
    required this.course,
  });
  final CourseModel course;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: CachedNetworkImage(
                imageUrl: course.imageUrl,
                fit: BoxFit.cover,
              )),
        ),
        const SizedBox(width: 20),
        Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  course.title,
                  textAlign: TextAlign.start,
                  style: const TextStyle(
                      fontWeight: FontWeight.bold, fontSize: 18),
                ),
                Text(
                  course.desc,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.left,
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text.rich(
                          TextSpan(
                              text: '${course.chapters.length} ',
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                              children: const [
                                TextSpan(
                                    text: "Chapters",
                                    style: TextStyle(
                                        fontWeight: FontWeight.normal))
                              ]),
                        ),
                        Text.rich(
                          TextSpan(
                              text: "By ",
                              children: [TextSpan(text: course.author)]),
                        ),
                      ],
                    ),
                    const SizedBox(width: 6),
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        if (value == 'delete') {
                          _onDelete(context);
                        } else if (value == 'duplicate') {
                          _onDuplicate(context);
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete),
                              SizedBox(width: 8),
                              Text('Delete'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'duplicate',
                          child: Row(
                            children: [
                              Icon(Icons.copy),
                              SizedBox(width: 8),
                              Text('Duplicate'),
                            ],
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ],
            ))
      ],
    );
  }

  _onDelete(context) async {
    try {
      final result = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Delete course permanently?'),
          content: const Text(
            'If you delete this course, you won\'t be able to recover it. Do you want to delete it?',
          ),
          actions: [
            TextButton(
              child: const Text('Delete'),
              onPressed: () {
                Navigator.pop(context, true);
                // Delete file here
              },
            ),
            ElevatedButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.pop(context, false),
            ),
          ],
        ),
      );
      if (result == true) {
        Get.find<CoursesCtrl>().deleteCourse(context, course.docId);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }

  _onDuplicate(context) async {
    try {
      final result = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Duplicate this course?'),
          content: const Text(
            'If you duplicate this course, it will create a copy of this course and by default it will be unavailabe. Do you want to duplicate it?',
          ),
          actions: [
            TextButton(
              child: const Text('Duplicate'),
              onPressed: () {
                Navigator.pop(context, true);
                // Delete file here
              },
            ),
            ElevatedButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.pop(context, false),
            ),
          ],
        ),
      );
      if (result == true) {
        Get.find<CoursesCtrl>().duplicateCourse(context, course);
      }
    } catch (e) {
      debugPrint(e.toString());
    }
  }
}
