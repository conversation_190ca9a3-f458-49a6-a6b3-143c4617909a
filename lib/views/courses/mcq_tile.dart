import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wellfedadmin/models/course_input_models.dart';

class MCQTileExpander extends StatelessWidget {
  const MCQTileExpander(
      {super.key,
      required this.count,
      required this.index,
      required this.idx,
      required this.i,
      required this.mcq,
      required this.removeAssignQuestion,
      required this.setState});
  final int count;
  final int index;
  final int idx;
  final int i;
  final MCQInputModel mcq;
  final Function setState;
  final Function removeAssignQuestion;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Card(
        color: Colors.grey[100],
        child: ExpansionTile(
          initiallyExpanded: true,
          trailing: IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => removeAssignQuestion(true, index, idx, i),
          ),
          title: Text(
            "MCQ ${i + 1}",
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  StaggeredGrid.count(
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                    crossAxisCount: count == 1 ? 1 : count - 1,
                    children: [
                      StaggeredGridTile.fit(
                        crossAxisCellCount: 2,
                        child: TextFormField(
                          controller: mcq.question,
                          decoration: const InputDecoration(
                            labelText: "Question",
                            hintText: "What is the question?",
                          ),
                          validator: (value) =>
                              value!.isEmpty ? "Required" : null,
                        ),
                      ),
                      ...List.generate(
                          mcq.options.length,
                          (ii) => Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Radio<int>(
                                    value: ii,
                                    groupValue: mcq.answerIndex,
                                    onChanged: (value) => setState(
                                        () => mcq.answerIndex = value ?? -1),
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: TextFormField(
                                      controller: mcq.options[ii],
                                      decoration: InputDecoration(
                                        hintText: "Option ${ii + 1}",
                                      ),
                                      validator: (value) =>
                                          value!.isEmpty ? "Required" : null,
                                    ),
                                  ),
                                ],
                              ))
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
