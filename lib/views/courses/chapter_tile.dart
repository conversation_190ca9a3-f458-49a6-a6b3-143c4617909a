import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:wellfedadmin/models/course_input_models.dart';
import 'package:wellfedadmin/utils/const.dart';
import 'package:wellfedadmin/views/courses/assign_tile.dart';
import 'package:wellfedadmin/views/courses/module_tile.dart';
import '../../models/time_duration.dart';
import '../../utils/methods.dart';

class ChapterTileExpander extends StatefulWidget {
  const ChapterTileExpander({
    super.key,
    required this.count,
    required this.index,
    required this.chapter,
    required this.removeAssignQuestion,
    required this.removeModule,
    required this.removeAssign,
    required this.removeChapter,
    required this.setState,
    this.selectedTile,
    required this.onTileChange,
  });
  final int count;
  final int index;
  final ChapterInputModel chapter;
  final Function setState;
  final Function removeChapter;
  final Function removeModule;
  final Function removeAssign;
  final Function removeAssignQuestion;
  final int? selectedTile;
  final Function(int) onTileChange;

  @override
  State<ChapterTileExpander> createState() => _ChapterTileExpanderState();
}

class _ChapterTileExpanderState extends State<ChapterTileExpander> {
  int? expandedModuleIdx;
  int? expandedAssignmentIdx;
  @override
  Widget build(BuildContext context) {
    final chapter = widget.chapter;
    final count = widget.count;
    final index = widget.index;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4),
      child: Card(
        color: Colors.grey[50],
        child: ExpansionTile(
          key: ValueKey<int>(
              widget.selectedTile == widget.index ? widget.index : -1),
          onExpansionChanged: (bool isOpen) {
            if (isOpen) {
              debugPrint("Open: ${widget.index}");
              widget.onTileChange(widget.index);
            } else {
              debugPrint("Close: ${widget.index}");
              widget.onTileChange(-1);
            }
          },
          trailing: IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => widget.removeChapter(widget.index),
          ),
          initiallyExpanded: widget.index == widget.selectedTile,
          title: Row(
            children: [
              Text(
                'Chapter ${widget.index + 1}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(width: 16),
              _buildCountChip('Modules', widget.chapter.modules.length),
              const SizedBox(width: 8),
              _buildCountChip('Assignments', widget.chapter.assignments.length),
            ],
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  StaggeredGrid.count(
                    crossAxisSpacing: 8,
                    crossAxisCount: 1,
                    // crossAxisCount: widget.count == 1 ? 1 : widget.count - 1,
                    children: [
                      TextFormField(
                        controller: widget.chapter.name,
                        decoration: const InputDecoration(
                          labelText: "Title",
                          hintText: "Chapter Name",
                        ),
                        validator: (value) =>
                            value!.isEmpty ? "Required" : null,
                      ),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              decoration:
                                  const InputDecoration(labelText: "Hours"),
                              initialValue:
                                  widget.chapter.duration.hours.toString(),
                              keyboardType: TextInputType.number,
                              onChanged: (value) => widget.setState(() => widget
                                  .chapter
                                  .duration
                                  .hours = int.tryParse(value) ?? 0),
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextFormField(
                              decoration:
                                  const InputDecoration(labelText: "Minutes"),
                              initialValue:
                                  widget.chapter.duration.minutes.toString(),
                              keyboardType: TextInputType.number,
                              onChanged: (value) => widget.setState(() => widget
                                  .chapter
                                  .duration
                                  .minutes = int.tryParse(value) ?? 0),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Row(
                      children: [
                        FilledButton(
                          child: const Text("Add Module"),
                          onPressed: () {
                            widget.chapter.modules.add(ModuleInputModel(
                                id: getRandomId(courseContentIdLen),
                                name: TextEditingController(),
                                duration: TimeDuration(hours: 0, minutes: 0),
                                content: TextEditingController(),
                                videoUrl: TextEditingController(),
                                minWatch: TextEditingController()));
                            widget.setState(() {});
                          },
                        ),
                        const SizedBox(width: 12),
                        FilledButton(
                          child: const Text("Add Assignment"),
                          onPressed: () {
                            widget.chapter.assignments.add(AssignmentInputModel(
                                id: getRandomId(courseContentIdLen),
                                name: TextEditingController(),
                                duration: TimeDuration(hours: 0, minutes: 0),
                                minPercentage: TextEditingController(),
                                mcqs: [],
                                matches: []));
                            widget.setState(() {});
                          },
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 20),
                  ...List.generate(
                    chapter.modules.length,
                    (idx) => ModuleTileExpander(
                      count: count,
                      index: index,
                      idx: idx,
                      module: chapter.modules[idx],
                      removeModule: widget.removeModule,
                      setState: setState,
                      isExpanded: expandedModuleIdx == idx,
                      onToggle: () {
                        setState(() {
                          expandedModuleIdx =
                              expandedModuleIdx == idx ? null : idx;
                        });
                      },
                    ),
                  ),
                  const SizedBox(height: 20),
                  ...List.generate(
                    chapter.assignments.length,
                    (idx) => AssignmentTileExpander(
                      count: count,
                      index: index,
                      idx: idx,
                      assign: chapter.assignments[idx],
                      removeAssign: widget.removeAssign,
                      setState: setState,
                      removeAssignQuestion: widget.removeAssignQuestion,
                      isExpanded: expandedAssignmentIdx == idx,
                      onToggle: () {
                        setState(() {
                          expandedAssignmentIdx =
                              expandedAssignmentIdx == idx ? null : idx;
                        });
                      },
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildCountChip(String label, int count) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.blue[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        '$label: $count',
        style: TextStyle(
          color: Colors.blue[800],
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
