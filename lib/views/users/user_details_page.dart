import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfedadmin/controllers/users_ctrl.dart';
import 'package:wellfedadmin/models/user_model.dart';
import 'package:wellfedadmin/utils/theme.dart';
import 'package:wellfedadmin/widgets/loader.dart';

class UserDetailsPage extends StatefulWidget {
  final String userId;

  const UserDetailsPage({super.key, required this.userId});

  @override
  State<UserDetailsPage> createState() => _UserDetailsPageState();
}

class _UserDetailsPageState extends State<UserDetailsPage> {
  late UsersCtrl controller;
  UserModel? user;
  bool isLoading = true;
  bool isEditing = false;

  // Form controllers
  final _formKey = GlobalKey<FormState>();
  late TextEditingController nameController;
  late TextEditingController emailController;
  late TextEditingController contactController;
  late TextEditingController branchController;
  late TextEditingController eIdController;

  @override
  void initState() {
    super.initState();
    controller = Get.find<UsersCtrl>();
    _loadUser();
  }

  Future<void> _loadUser() async {
    try {
      final userData = await controller.getUserById(widget.userId);
      if (userData != null) {
        setState(() {
          user = userData;
          _initializeControllers();
          isLoading = false;
        });
      } else {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  void _initializeControllers() {
    if (user != null) {
      nameController = TextEditingController(text: user!.name);
      emailController = TextEditingController(text: user!.email);
      contactController = TextEditingController(text: user!.contact);
      branchController = TextEditingController(text: user!.branch);
      eIdController = TextEditingController(text: user!.eId ?? '');
    }
  }

  @override
  void dispose() {
    if (user != null) {
      nameController.dispose();
      emailController.dispose();
      contactController.dispose();
      branchController.dispose();
      eIdController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      body: SafeArea(
        child: isLoading
            ? Center(child: loaderCircular())
            : user == null
                ? _buildUserNotFound()
                : _buildUserDetails(),
      ),
    );
  }

  Widget _buildUserNotFound() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_off_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'User not found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildUserDetails() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 24),
          Expanded(
            child: SingleChildScrollView(
              child: _buildUserForm(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back),
          style: IconButton.styleFrom(
            backgroundColor: surfaceColor,
            foregroundColor: Colors.black87,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isEditing ? 'Edit User' : 'User Details',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                user!.name,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            ],
          ),
        ),
        if (!isEditing)
          ElevatedButton.icon(
            onPressed: () => setState(() => isEditing = true),
            icon: const Icon(Icons.edit, size: 18),
            label: const Text('Edit'),
          ),
        if (isEditing) ...[
          OutlinedButton(
            onPressed: _cancelEdit,
            child: const Text('Cancel'),
          ),
          const SizedBox(width: 12),
          ElevatedButton.icon(
            onPressed: _saveUser,
            icon: const Icon(Icons.save, size: 18),
            label: const Text('Save'),
          ),
        ],
      ],
    );
  }

  Widget _buildUserForm() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Avatar and basic info
              Row(
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: primaryColor.withValues(alpha: 0.1),
                    child: Text(
                      user!.name.isNotEmpty ? user!.name[0].toUpperCase() : '?',
                      style: TextStyle(
                        color: primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                      ),
                    ),
                  ),
                  const SizedBox(width: 24),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'User ID: ${user!.docId}',
                          style:
                              Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.grey[600],
                                    fontFamily: 'monospace',
                                  ),
                        ),
                        const SizedBox(height: 8),
                        if (user!.createdBy != null)
                          Text(
                            'Created by: ${user!.createdBy}',
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.grey[600],
                                    ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 32),
              const Divider(),
              const SizedBox(height: 24),

              // Form fields
              _buildFormField(
                label: 'Full Name',
                controller: nameController,
                enabled: isEditing,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Name is required';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              _buildFormField(
                label: 'Email',
                controller: emailController,
                enabled: isEditing,
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Email is required';
                  }
                  if (!GetUtils.isEmail(value.trim())) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              _buildFormField(
                label: 'Contact',
                controller: contactController,
                enabled: isEditing,
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Contact is required';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              _buildFormField(
                label: 'Branch',
                controller: branchController,
                enabled: isEditing,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Branch is required';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              _buildFormField(
                label: 'Employee ID (Optional)',
                controller: eIdController,
                enabled: isEditing,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required bool enabled,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          enabled: enabled,
          keyboardType: keyboardType,
          validator: validator,
          decoration: InputDecoration(
            filled: true,
            fillColor: enabled ? surfaceColor : Colors.grey[100],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: primaryColor, width: 2),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[200]!),
            ),
          ),
        ),
      ],
    );
  }

  void _cancelEdit() {
    setState(() {
      isEditing = false;
      _initializeControllers(); // Reset form values
    });
  }

  Future<void> _saveUser() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      isLoading = true;
    });

    try {
      final updatedUser = UserModel(
        docId: user!.docId,
        name: nameController.text.trim(),
        email: emailController.text.trim(),
        contact: contactController.text.trim(),
        branch: branchController.text.trim(),
        eId: eIdController.text.trim().isEmpty
            ? null
            : eIdController.text.trim(),
        createdBy: user!.createdBy,
        cartItems: user!.cartItems,
      );

      await controller.updateUser(context, updatedUser);

      setState(() {
        user = updatedUser;
        isEditing = false;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving user: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
