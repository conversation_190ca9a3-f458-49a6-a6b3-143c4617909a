import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfedadmin/controllers/users_ctrl.dart';
import 'package:wellfedadmin/models/user_model.dart';
import 'package:wellfedadmin/utils/theme.dart';
import 'package:wellfedadmin/views/users/user_details_page.dart';
import 'package:wellfedadmin/widgets/loader.dart';
import 'package:wellfedadmin/widgets/pagination_widget.dart';

class UsersPage extends StatefulWidget {
  const UsersPage({super.key});

  @override
  State<UsersPage> createState() => _UsersPageState();
}

class _UsersPageState extends State<UsersPage> {
  late UsersCtrl controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(UsersCtrl());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSearchBar(),
              const SizedBox(height: 24),
              Expanded(
                child: GetBuilder<UsersCtrl>(
                  builder: (controller) {
                    if (controller.isLoading &&
                        controller.filteredUsers.isEmpty) {
                      return Center(child: loaderCircular());
                    }

                    if (controller.filteredUsers.isEmpty &&
                        !controller.isLoading) {
                      return _buildEmptyState();
                    }

                    return Column(
                      children: [
                        Expanded(
                          child: _buildUsersList(controller),
                        ),
                        const SizedBox(height: 16),
                        PaginationWidget(
                          currentPage: controller.currentPage,
                          totalPages: controller.totalPages,
                          onPageChanged: (page) => controller.loadPage(page),
                          isLoading: controller.isLoading,
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return TextField(
      controller: controller.searchCtrl,
      decoration: const InputDecoration(
        hintText: 'Search',
        prefixIcon: Icon(Icons.search),
        border: InputBorder.none,
        // contentPadding: EdgeInsets.symmetric(vertical: 16),
      ),
    );
  }

  Widget _buildUsersList(UsersCtrl controller) {
    final int itemsCount = controller.filteredUsers.length;
    return Column(
      children: [
        // List Header
        _buildListHeader(),
        const Divider(height: 1, color: Colors.black),
        // List Items
        Expanded(
          child: ListView.separated(
            physics: const NeverScrollableScrollPhysics(), // Disable scrolling
            itemCount: itemsCount,
            separatorBuilder: (context, index) =>
                const Divider(height: 1, color: Colors.black12),
            itemBuilder: (context, index) {
              final user = controller.filteredUsers[index];
              return _buildUserListItem(user);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildListHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: primaryColor.withValues(alpha: 0.05),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          const SizedBox(width: 56), // Space for avatar
          Expanded(
            flex: 2,
            child: Text(
              'Name',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: primaryColor,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              'Email',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: primaryColor,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'Branch',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: primaryColor,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'Contact',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: primaryColor,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'Sale',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: primaryColor,
                fontSize: 14,
              ),
            ),
          ),
          const SizedBox(width: 48), // Space for action button
        ],
      ),
    );
  }

  Widget _buildUserListItem(UserModel user) {
    final userCourses = controller.getCoursesForUser(user.docId);
    print('Courses for ${user.name}: ${userCourses.length}');
    final isEnterpriseUser = userCourses.any((course) => course.isEnterprise);
    print('Is Enterprise User: $isEnterpriseUser');
    return InkWell(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => UserDetailsPage(userId: user.docId),
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Avatar
            CircleAvatar(
              radius: 20,
              backgroundColor: primaryColor.withValues(alpha: 0.1),
              child: isEnterpriseUser
                  ? Icon(
                      Icons
                          .apartment, // or another icon representing enterprise
                      color: primaryColor,
                      size: 20,
                    )
                  : Text(
                      user.name.isNotEmpty ? user.name[0].toUpperCase() : '?',
                      style: TextStyle(
                        color: primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
            ),
            const SizedBox(width: 16),

            // Name
            Expanded(
              flex: 2,
              child: Text(
                user.name,
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Email
            Expanded(
              flex: 3,
              child: Text(
                user.email,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Branch
            Expanded(
              flex: 2,
              child: Text(
                user.branch,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Contact
            Expanded(
              flex: 2,
              child: Text(
                user.contact,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Expanded(
              flex: 2,
              child: Text(
                '\$${user.valuePurchased?.toString() ?? '0'}',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // Delete button
            IconButton(
              onPressed: () => _showDeleteDialog(user),
              icon: const Icon(Icons.delete_outline, size: 20),
              color: Colors.red[400],
              tooltip: 'Delete user',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'No users found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search criteria',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[500],
                ),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Text(
            'Are you sure you want to delete ${user.name}? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              controller.deleteUser(context, user.docId);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
