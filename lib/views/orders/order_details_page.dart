import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfedadmin/controllers/orders_ctrl.dart';
import 'package:wellfedadmin/models/orders_model.dart';
import 'package:wellfedadmin/models/user_course_model.dart';
import 'package:wellfedadmin/utils/theme.dart';
import 'package:wellfedadmin/widgets/loader.dart';

class OrderDetailsPage extends StatefulWidget {
  final String orderId;

  const OrderDetailsPage({super.key, required this.orderId});

  @override
  State<OrderDetailsPage> createState() => _OrderDetailsPageState();
}

class _OrderDetailsPageState extends State<OrderDetailsPage> {
  final OrdersCtrl controller = Get.find<OrdersCtrl>();

  OrderModel? order;
  bool isLoading = true;
  bool isEditing = false;

  // Form controllers
  final _formKey = GlobalKey<FormState>();
  late TextEditingController totalAmountController;
  late TextEditingController discountController;
  late TextEditingController originalController;
  bool fromCart = false;
  bool processed = false;

  @override
  void initState() {
    super.initState();
    _loadOrder();
  }

  Future<void> _loadOrder() async {
    try {
      final orderData = await controller.getOrderById(widget.orderId);
      if (orderData != null) {
        setState(() {
          order = orderData;
          _initializeControllers();
          isLoading = false;
        });
      } else {
        setState(() {
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  void _initializeControllers() {
    if (order != null) {
      totalAmountController =
          TextEditingController(text: order!.totalAmount.toString());
      discountController =
          TextEditingController(text: order!.discount.toString());
      originalController =
          TextEditingController(text: order!.original.toString());
      fromCart = order!.fromCart;
      processed = order!.processed;
    }
  }

  @override
  void dispose() {
    if (order != null) {
      totalAmountController.dispose();
      discountController.dispose();
      originalController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      body: SafeArea(
        child: isLoading
            ? Center(child: loaderCircular())
            : order == null
                ? _buildOrderNotFound()
                : _buildOrderDetails(),
      ),
    );
  }

  Widget _buildOrderNotFound() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Order not found',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  color: Colors.grey[600],
                ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Go Back'),
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetails() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 32),
          _buildOrderCard(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back),
          style: IconButton.styleFrom(
            backgroundColor: surfaceColor,
            foregroundColor: Colors.black87,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Order Details',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                'Order ID: ${order!.docId}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),
            ],
          ),
        ),
        if (!isEditing)
          ElevatedButton.icon(
            onPressed: () => setState(() => isEditing = true),
            icon: const Icon(Icons.edit),
            label: const Text('Edit'),
            style: ElevatedButton.styleFrom(
              backgroundColor: primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        if (isEditing) ...[
          ElevatedButton.icon(
            onPressed: _saveOrder,
            icon: const Icon(Icons.save),
            label: const Text('Save'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
          const SizedBox(width: 8),
          OutlinedButton(
            onPressed: _cancelEdit,
            child: const Text('Cancel'),
          ),
        ],
      ],
    );
  }

  Widget _buildOrderCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order info header
              Row(
                children: [
                  Icon(
                    Icons.shopping_cart,
                    color: primaryColor,
                    size: 32,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Order Information',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: primaryColor,
                                  ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'User: ${controller.getUserName(order!.uid)}',
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Form fields
              _buildFormField(
                label: 'Total Amount',
                controller: totalAmountController,
                enabled: isEditing,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Total amount is required';
                  }
                  if (double.tryParse(value.trim()) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              _buildFormField(
                label: 'Discount',
                controller: discountController,
                enabled: isEditing,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value != null && value.trim().isNotEmpty) {
                    if (double.tryParse(value.trim()) == null) {
                      return 'Please enter a valid number';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              _buildFormField(
                label: 'Original Amount',
                controller: originalController,
                enabled: isEditing,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Original amount is required';
                  }
                  if (double.tryParse(value.trim()) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Boolean fields
              Row(
                children: [
                  Expanded(
                    child: _buildSwitchField(
                      label: 'From Cart',
                      value: fromCart,
                      enabled: isEditing,
                      onChanged: (value) => setState(() => fromCart = value),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildSwitchField(
                      label: 'Processed',
                      value: processed,
                      enabled: isEditing,
                      onChanged: (value) => setState(() => processed = value),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Order time
              if (order!.time != null) ...[
                Text(
                  'Order Time',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: primaryColor,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  order!.time!.toString(),
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 16),
              ],

              // Checkout courses
              if (order!.checkoutCourses.isNotEmpty) ...[
                Text(
                  'Courses (${order!.checkoutCourses.length})',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: primaryColor,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                ...order!.checkoutCourses.map((course) => Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Text(
                        '• Course ID: ${course.courseId} (Qty: ${course.qty})',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    )),
                const SizedBox(height: 16),
              ],

              // User Courses with End Date Extension
              _buildUserCoursesSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormField({
    required String label,
    required TextEditingController controller,
    required bool enabled,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: primaryColor,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          enabled: enabled,
          keyboardType: keyboardType,
          validator: validator,
          decoration: InputDecoration(
            filled: true,
            fillColor: enabled ? surfaceColor : Colors.grey[100],
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: primaryColor, width: 2),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[200]!),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSwitchField({
    required String label,
    required bool value,
    required bool enabled,
    required Function(bool) onChanged,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: primaryColor,
            fontSize: 16,
          ),
        ),
        Switch(
          value: value,
          onChanged: enabled ? onChanged : null,
          activeColor: primaryColor,
        ),
      ],
    );
  }

  Widget _buildUserCoursesSection() {
    final userCourses = controller.getUserCoursesForOrder(order!);

    if (userCourses.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'User Courses',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: primaryColor,
            fontSize: 16,
          ),
        ),
        const SizedBox(height: 8),
        ...userCourses.map((userCourse) => _buildUserCourseItem(userCourse)),
      ],
    );
  }

  Widget _buildUserCourseItem(UserCourseModel userCourse) {
    final endDate = userCourse.endDate?.toDate();
    final isExpired = endDate != null && endDate.isBefore(DateTime.now());

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Course ID: ${userCourse.courseId}',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isExpired ? Colors.red[100] : Colors.green[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isExpired ? 'Expired' : 'Active',
                    style: TextStyle(
                      color: isExpired ? Colors.red[800] : Colors.green[800],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Expanded(
                  child: Text(
                    'End Date: ${endDate != null ? "${endDate.day}/${endDate.month}/${endDate.year}" : "Not set"}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                ),
                if (isEditing) ...[
                  ElevatedButton.icon(
                    onPressed: () => _showExtendDateDialog(userCourse),
                    icon: const Icon(Icons.calendar_today, size: 16),
                    label: const Text('Extend'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: primaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _cancelEdit() {
    setState(() {
      isEditing = false;
      _initializeControllers(); // Reset form values
    });
  }

  Future<void> _saveOrder() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      isLoading = true;
    });

    try {
      final updatedOrder = OrderModel(
        orderId: order!.orderId,
        docId: order!.docId,
        uid: order!.uid,
        checkoutCourses: order!.checkoutCourses,
        totalAmount: double.parse(totalAmountController.text.trim()),
        discount: double.parse(discountController.text.trim().isEmpty
            ? '0'
            : discountController.text.trim()),
        fromCart: fromCart,
        original: double.parse(originalController.text.trim()),
        processed: processed,
        time: order!.time,
      );

      await controller.updateOrder(context, updatedOrder);

      setState(() {
        order = updatedOrder;
        isEditing = false;
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showExtendDateDialog(UserCourseModel userCourse) {
    final currentEndDate = userCourse.endDate?.toDate() ?? DateTime.now();
    DateTime selectedDate = currentEndDate.isAfter(DateTime.now())
        ? currentEndDate
        : DateTime.now().add(const Duration(days: 30));

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: const Text('Extend Course End Date'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Course ID: ${userCourse.courseId}'),
              const SizedBox(height: 16),
              Text(
                  'Current End Date: ${currentEndDate.day}/${currentEndDate.month}/${currentEndDate.year}'),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('New End Date: '),
                  TextButton(
                    onPressed: () async {
                      final date = await showDatePicker(
                        context: context,
                        initialDate: selectedDate,
                        firstDate: DateTime.now(),
                        lastDate:
                            DateTime.now().add(const Duration(days: 365 * 2)),
                      );
                      if (date != null) {
                        setDialogState(() {
                          selectedDate = date;
                        });
                      }
                    },
                    child: Text(
                        '${selectedDate.day}/${selectedDate.month}/${selectedDate.year}'),
                  ),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await controller.updateUserCourseEndDate(
                    context, userCourse.docId, selectedDate);
                setState(() {}); // Refresh the UI
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('Update'),
            ),
          ],
        ),
      ),
    );
  }
}
