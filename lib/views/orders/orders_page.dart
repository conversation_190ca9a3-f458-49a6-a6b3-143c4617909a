import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:wellfedadmin/controllers/orders_ctrl.dart';
import 'package:wellfedadmin/models/orders_model.dart';
import 'package:wellfedadmin/utils/theme.dart';
import 'package:wellfedadmin/views/orders/order_details_page.dart';
import 'package:wellfedadmin/widgets/date_picker_widget.dart';
import 'package:wellfedadmin/widgets/loader.dart';
import 'package:wellfedadmin/widgets/pagination_widget.dart';

class OrdersPage extends StatefulWidget {
  const OrdersPage({super.key});

  @override
  State<OrdersPage> createState() => _OrdersPageState();
}

class _OrdersPageState extends State<OrdersPage> {
  // final OrdersCtrl controller = Get.put(OrdersCtrl());

  String formatDateTime(DateTime dateTime) {
    final hour12 = dateTime.hour % 12 == 0 ? 12 : dateTime.hour % 12;
    final amPm = dateTime.hour >= 12 ? 'PM' : 'AM';
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final day = dateTime.day.toString().padLeft(2, '0');
    final month = dateTime.month.toString().padLeft(2, '0');
    final year = dateTime.year;
    return "$day/$month/$year, $hour12:$minute $amPm";
  }

  String getMonthName(int month) {
    const months = [
      'January',
      'February',
      'March',
      'April',
      'May',
      'June',
      'July',
      'August',
      'September',
      'October',
      'November',
      'December'
    ];
    return months[month - 1];
  }

  List<DropdownMenuItem<DateTime>> _generateMonthItems(OrdersCtrl controller) {
    final months = controller.monthsWithOrders; // Uses controller property
    return months.map((monthDate) {
      return DropdownMenuItem<DateTime>(
        value: monthDate,
        child: Text('${getMonthName(monthDate.month)} ${monthDate.year}'),
      );
    }).toList();
  }

  @override
  void initState() {
    super.initState();
    Get.put(OrdersCtrl());
    // Controller will automatically load current month's orders in onInit
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      body: GetBuilder<OrdersCtrl>(builder: (controller) {
        if (controller.isLoading) {
          return Center(child: loaderCircular());
        }
        if (controller.filteredOrders.isEmpty) {
          return _buildEmptyState(controller);
        }
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(child: _buildSearchBar(controller)),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: DropdownButton<DateTime>(
                        value: controller.selectedMonth,
                        underline: const SizedBox(),
                        items: _generateMonthItems(
                          controller,
                        ),
                        onChanged: (DateTime? newMonth) async {
                          if (newMonth != null) {
                            controller.selectedMonth = newMonth;
                            controller.update();
                            await controller.loadOrdersForMonth(newMonth);
                          }
                        },
                      ),
                    ),

                    const SizedBox(width: 8),
                    // Date Picker
                    DatePickerWidget(
                      firstDate: DateTime(2022),
                      lastDate: DateTime(2100),
                      initialDate: controller.selectedDate ?? DateTime.now(),
                      onDateChanged: (newDate) async {
                        await controller.loadOrdersForDate(newDate);
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Total Orders: ${controller.totalOrders}  |  Active: ${controller.activeOrdersCountVal}  |  Expired: ${controller.expiredOrdersCountVal}',
                        style: TextStyle(
                          // fontWeight: FontWeight.w600,
                          color: primaryColor,
                          fontSize: 16,
                        ),
                      ),
                    ),
                    // Month Dropdown

                    const SizedBox(width: 16),
                    // Clear filters button
                    TextButton(
                      style: TextButton.styleFrom(
                        // backgroundColor: Colors.white,
                        // foregroundColor: primaryColor,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 2,
                      ),
                      onPressed: () async {
                        await controller.loadOrdersForMonth(DateTime(
                            DateTime.now().year, DateTime.now().month));
                      },
                      child: const Text(
                        '➤  Go To Current Month',
                        style: TextStyle(
                          // fontWeight: FontWeight.w600,
                          color: primaryColor,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                Expanded(
                    child: Column(
                  children: [
                    Expanded(
                      child: _buildOrdersList(controller),
                    ),
                    const SizedBox(height: 16),
                    PaginationWidget(
                      currentPage: controller.currentPage,
                      totalPages: controller.totalPages,
                      onPageChanged: (page) async {
                        if (controller.selectedDate != null) {
                          await controller.loadOrdersForDate(
                              controller.selectedDate!,
                              refresh: false);
                        } else {
                          await controller
                              .loadOrdersForMonth(controller.selectedMonth);
                        }
                      },
                      isLoading: controller.isLoading,
                    ),
                  ],
                )),
              ],
            ),
          ),
        );
      }),
    );
  }

  Widget _buildSearchBar(OrdersCtrl controller) {
    return TextField(
      controller: controller.searchCtrl,
      decoration: const InputDecoration(
        hintText: 'Search',
        prefixIcon: Icon(Icons.search),
        border: InputBorder.none,
      ),
      onChanged: (_) => controller.onSearchChanged(), // Explicit call
    );
  }

  Widget _buildOrdersList(OrdersCtrl controller) {
    final itemsCount = controller.filteredOrders.length;
    return Column(
      children: [
        _buildListHeader(),
        const Divider(height: 1, color: Colors.black),
        Expanded(
          child: ListView.separated(
            itemCount: itemsCount,
            separatorBuilder: (context, index) =>
                const Divider(height: 1, color: Colors.black12),
            itemBuilder: (context, index) {
              final order = controller.filteredOrders[index];
              return _buildOrderListItem(order, controller);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildListHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: primaryColor.withValues(alpha: 0.05),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Expanded(
              flex: 1,
              child: Text('Order Id',
                  style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: primaryColor,
                      fontSize: 14))),
          Expanded(
              flex: 3,
              child: Text('Name',
                  style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: primaryColor,
                      fontSize: 14))),
          Expanded(
              flex: 2,
              child: Text('Type',
                  style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: primaryColor,
                      fontSize: 14))),
          Expanded(
              flex: 2,
              child: Center(
                  child: Text('Course Ids',
                      style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: primaryColor,
                          fontSize: 14)))),
          Expanded(
              flex: 2,
              child: Center(
                  child: Text('Qty',
                      style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: primaryColor,
                          fontSize: 14)))),
          Expanded(
              flex: 2,
              child: Text('Total Amount',
                  style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: primaryColor,
                      fontSize: 14))),
          Expanded(
              flex: 2,
              child: Center(
                  child: Text('Status',
                      style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: primaryColor,
                          fontSize: 14)))),
          Expanded(
              flex: 2,
              child: Center(
                  child: Text('Date & Time',
                      style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: primaryColor,
                          fontSize: 14)))),
          const SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildOrderListItem(OrderModel order, OrdersCtrl controller) {
    return InkWell(
      onTap: () {
        Navigator.of(context).push(
          MaterialPageRoute(
              builder: (context) => OrderDetailsPage(orderId: order.docId)),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Expanded(
                flex: 1,
                child: Text(order.orderId,
                    style: const TextStyle(
                        fontWeight: FontWeight.w500, fontSize: 14),
                    overflow: TextOverflow.ellipsis)),
            Expanded(
                flex: 3,
                child: Text(controller.getUserName(order.uid),
                    style: const TextStyle(
                        fontWeight: FontWeight.w500, fontSize: 14),
                    overflow: TextOverflow.ellipsis)),
            Expanded(
                flex: 2,
                child: Text(
                    controller.userIsEnterpriseMap[order.uid] == true
                        ? 'Enterprise'
                        : 'Retail',
                    style: const TextStyle(
                        fontWeight: FontWeight.w500, fontSize: 14),
                    overflow: TextOverflow.ellipsis)),
            Expanded(
                flex: 2,
                child: Center(
                    child: Text(
                        controller.getCourseIdsForOrder(order).join(', '),
                        style: TextStyle(
                            color: Colors.green[700],
                            fontSize: 12,
                            fontWeight: FontWeight.w500),
                        textAlign: TextAlign.center))),
            Expanded(
                flex: 2,
                child: Center(
                    child: Text(
                        order.checkoutCourses
                            .map((e) => e.qty.toString())
                            .join(', '),
                        style: TextStyle(
                            color: Colors.green[700],
                            fontSize: 12,
                            fontWeight: FontWeight.w500),
                        textAlign: TextAlign.center))),
            Expanded(
                flex: 2,
                child: Text('\$${order.totalAmount.toStringAsFixed(2)}',
                    style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                        fontWeight: FontWeight.w500),
                    overflow: TextOverflow.ellipsis)),
            Expanded(
                flex: 2,
                child: Center(
                    child: _buildStatusChip(controller.getOrderStatus(order)))),
            Expanded(
                flex: 2,
                child: Center(
                    child: Text(
                        order.time != null ? formatDateTime(order.time!) : '—',
                        style: TextStyle(
                            color: Colors.grey[800],
                            fontSize: 12,
                            fontWeight: FontWeight.w500),
                        textAlign: TextAlign.center))),
            SizedBox(
              width: 48,
              child: IconButton(
                onPressed: () => _showDeleteDialog(order, controller),
                icon: const Icon(Icons.delete_outline, size: 20),
                color: Colors.red[400],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color backgroundColor;
    Color textColor;

    switch (status.toLowerCase()) {
      case 'active':
        backgroundColor = Colors.green[100]!;
        textColor = Colors.green[800]!;
        break;
      case 'expired':
        backgroundColor = Colors.red[100]!;
        textColor = Colors.red[800]!;
        break;
      default:
        backgroundColor = Colors.grey[100]!;
        textColor = Colors.grey[800]!;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status,
        style: TextStyle(
          color: textColor,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildEmptyState(OrdersCtrl controller) {
    final hasActiveFilter = controller.selectedDate != null ||
        controller.selectedMonth !=
            DateTime(DateTime.now().year, DateTime.now().month) ||
        (controller.searchCtrl.text.isNotEmpty ?? false);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.shopping_cart_outlined, size: 64, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text('No orders found',
              style: Theme.of(context)
                  .textTheme
                  .titleLarge
                  ?.copyWith(color: Colors.grey[600])),
          const SizedBox(height: 8),
          Text('Search',
              style: Theme.of(context)
                  .textTheme
                  .bodyMedium
                  ?.copyWith(color: Colors.grey[500])),
          const SizedBox(height: 16),
          if (hasActiveFilter)
            TextButton(
              style: TextButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 2,
              ),
              onPressed: () async {
                controller.searchCtrl.clear();
                controller.selectedDate = null;
                controller.selectedMonth =
                    DateTime(DateTime.now().year, DateTime.now().month);
                await controller.loadOrdersForMonth(controller.selectedMonth);
                controller.update();
              },
              child: const Text(
                'Clear Filters',
                style: TextStyle(
                  color: primaryColor,
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }

  void _showDeleteDialog(OrderModel order, OrdersCtrl controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Order'),
        content: Text(
            'Are you sure you want to delete this order? This action cannot be undone.'),
        actions: [
          TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await controller.deleteOrder(context, order.docId);
              if (controller.selectedDate != null) {
                await controller.loadOrdersForDate(controller.selectedDate!);
              } else {
                await controller.loadOrdersForMonth(controller.selectedMonth);
              }
            },
            style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red, foregroundColor: Colors.white),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
