import 'package:flutter/material.dart';
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:wellfedadmin/utils/const.dart';

Widget loaderDrop({Color? color, double? size}) =>
    LoadingAnimationWidget.inkDrop(
        color: color ?? Colors.white, size: size ?? 20);

Widget loaderWaveDots({Color? color, double? size}) =>
    LoadingAnimationWidget.staggeredDotsWave(
        color: color ?? Colors.white, size: size ?? 20);

Widget loaderCircular({Color? color, double? size}) =>
    LoadingAnimationWidget.discreteCircle(
        color: color ?? appColorOne,
        secondRingColor: appColorTwo,
        size: size ?? 20);
