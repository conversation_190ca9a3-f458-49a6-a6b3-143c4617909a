// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:wellfedadmin/utils/methods.dart';
import 'package:wellfedadmin/utils/theme.dart';
import 'package:wellfedadmin/utils/firebase.dart';
import 'package:wellfedadmin/views/courses/course_detail_view.dart';

class AdminLayout extends StatefulWidget {
  final Widget child;
  final String title;
  final int selectedIndex;
  final Function(int) onNavigationChanged;
  final List<AdminNavigationItem> navigationItems;

  const AdminLayout({
    super.key,
    required this.child,
    required this.title,
    required this.selectedIndex,
    required this.onNavigationChanged,
    required this.navigationItems,
  });

  @override
  State<AdminLayout> createState() => _AdminLayoutState();
}

class _AdminLayoutState extends State<AdminLayout> {
  bool isDrawerExpanded = true;

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isDesktop = screenWidth >= 1200;
    final isTablet = screenWidth >= 768 && screenWidth < 1200;
    final isMobile = screenWidth < 768;

    // Adjust drawer expansion based on screen size
    if (isMobile) {
      isDrawerExpanded = false;
    } else if (isTablet && screenWidth < 900) {
      isDrawerExpanded = false;
    }

    return Scaffold(
      key: scaffoldKey,
      backgroundColor: backgroundColor,
      body: SafeArea(
        child: Row(
          children: [
            // Left Sidebar/Drawer
            if (isDesktop || isTablet)
              _buildPersistentDrawer(isDesktop)
            else
              const SizedBox.shrink(),

            // Main Content Area
            Expanded(
              child: Column(
                children: [
                  // Top App Bar (only for content area)
                  _buildTopAppBar(context, isMobile),

                  // Main Content
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(isMobile ? 16 : 24),
                      child: widget.child,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),

      // Mobile Drawer
      drawer: isMobile ? _buildMobileDrawer() : null,
    );
  }

  Widget _buildPersistentDrawer(bool isDesktop) {
    final drawerWidth = isDrawerExpanded ? 280.0 : 80.0;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      width: drawerWidth,
      child: Container(
        decoration: BoxDecoration(
          color: drawerColor,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(2, 0),
            ),
          ],
        ),
        child: Column(
          children: [
            // Logo Section
            _buildLogoSection(),

            // Divider
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              height: 1,
              color: Colors.white.withValues(alpha: 0.1),
            ),

            // Navigation Items
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(vertical: 16),
                itemCount: widget.navigationItems.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: _buildNavigationItem(
                      widget.navigationItems[index],
                      index,
                      isExpanded: isDrawerExpanded,
                    ),
                  );
                },
              ),
            ),

            // Collapse/Expand Button (Desktop only)
            if (isDesktop) _buildCollapseButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileDrawer() {
    return Drawer(
      backgroundColor: drawerColor,
      child: Column(
        children: [
          // Logo Section
          _buildLogoSection(),

          // Navigation Items
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 8),
              itemCount: widget.navigationItems.length,
              itemBuilder: (context, index) {
                return _buildNavigationItem(
                  widget.navigationItems[index],
                  index,
                  isExpanded: true,
                  isMobile: true,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

// 1. Choose a modern, neutral drawer color for good contrast
  // const Color drawerColor = Color(0xFF23272E); // Example: dark slate

  Widget _buildLogoSection() {
    return LayoutBuilder(
      builder: (context, constraints) {
        double drawerWidth = constraints.maxWidth;
        double logoSize = (drawerWidth * 0.6).clamp(48.0, 96.0);

        return Container(
          height: logoSize + 32, // breathing room
          alignment: Alignment.center,
          child: Container(
            // Circular contrast background behind logo
            width: logoSize,
            height: logoSize,
            decoration: const BoxDecoration(
              color: Colors.white, // or Colors.grey[200], or your accent
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 8,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Padding(
              padding:
                  const EdgeInsets.all(8), // Prevent logo edges from cutting
              child: ClipOval(
                child: Image.asset(
                  'assets/logo.png',
                  width: logoSize - 16,
                  height: logoSize - 16,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      Icons.restaurant_menu,
                      color: primaryColor,
                      size: logoSize * 0.7,
                    );
                  },
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildNavigationItem(
    AdminNavigationItem item,
    int index, {
    bool isExpanded = true,
    bool isMobile = false,
  }) {
    final isSelected = widget.selectedIndex == index;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () async {
            widget.onNavigationChanged(index);
            if (item.route == 'logout') {
              // Close drawer if mobile before showing dialog
              if (isMobile || Scaffold.of(context).isDrawerOpen) {
                Navigator.of(context).pop();
              }
              await Future.delayed(const Duration(milliseconds: 200));
              _showLogoutDialog(context);
            } else {
              widget.onNavigationChanged(index);
              if (isMobile) {
                Navigator.of(context).pop();
              }
            }
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: isSelected ? drawerSelectedColor.withOpacity(0.2) : null,
              borderRadius: BorderRadius.circular(8),
              border: isSelected
                  ? Border.all(color: drawerSelectedColor.withOpacity(0.3))
                  : null,
            ),
            child: Row(
              children: [
                Icon(
                  item.icon,
                  color: isSelected ? drawerSelectedColor : drawerTextColor,
                  size: 20,
                ),
                if (isExpanded) ...[
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      item.label,
                      style: TextStyle(
                        color:
                            isSelected ? drawerSelectedColor : drawerTextColor,
                        fontSize: 14,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCollapseButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            setState(() {
              isDrawerExpanded = !isDrawerExpanded;
            });
          },
          child: Container(
            padding: const EdgeInsets.all(8),
            child: Icon(
              isDrawerExpanded ? Icons.chevron_left : Icons.chevron_right,
              color: drawerTextColor,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTopAppBar(BuildContext context, bool isMobile) {
    return Container(
      height: 64,
      decoration: const BoxDecoration(
        color: surfaceColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 2,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          // Mobile Menu Button
          if (isMobile)
            IconButton(
              icon: const Icon(Icons.menu),
              onPressed: () => scaffoldKey.currentState?.openDrawer(),
              // Scaffold.of(context).openDrawer(),
            ),

          // Page Title
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: isMobile ? 0 : 24),
              child: Text(
                widget.title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
            ),
          ),

          // Action Buttons
          if (widget.title == 'Course Management')
            Padding(
              padding: const EdgeInsets.only(right: 24),
              child: ElevatedButton.icon(
                onPressed: () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const CourseView()),
                ),
                icon: const Icon(Icons.add, size: 18),
                label: Text(isMobile ? 'Add' : 'Add New Course'),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: isMobile ? 16 : 24,
                    vertical: isMobile ? 12 : 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),

          // _buildTopBarActions(),
        ],
      ),
    );
  }

  Widget _buildTopBarActions() {
    return Padding(
      padding: const EdgeInsets.only(right: 24),
      child: ElevatedButton.icon(
        onPressed: () {
          if (Scaffold.of(context).isDrawerOpen) {
            Navigator.of(context).pop();
          }
          _showLogoutDialog(context);
        },
        // _showLogoutDialog(context),
        icon: const Icon(
          Icons.logout,
          size: 18,
        ),
        label: const Text(
          'Logout',
          style: TextStyle(
            fontWeight: FontWeight.w500,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.red.shade600,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          elevation: 2,
        ),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) async {
    // Close drawer if open on mobile (optional safeguard)
    if (Scaffold.of(context).isDrawerOpen) {
      Navigator.of(context).pop();
      // Wait for drawer to close before showing dialog
      await Future.delayed(const Duration(milliseconds: 300));
    }
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              FBAuth.auth.signOut();
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}

class AdminNavigationItem {
  final IconData icon;
  final String label;
  final String route;

  const AdminNavigationItem({
    required this.icon,
    required this.label,
    required this.route,
  });
}
