import 'package:flutter/material.dart';
import 'package:wellfedadmin/utils/theme.dart';

class PaginationWidget extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final Function(int) onPageChanged;
  final bool isLoading;

  const PaginationWidget({
    super.key,
    required this.currentPage,
    required this.totalPages,
    required this.onPageChanged,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    if (totalPages <= 1) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Previous button
          _buildNavigationButton(
            context,
            icon: Icons.chevron_left,
            onPressed: currentPage > 1 && !isLoading
                ? () => onPageChanged(currentPage - 1)
                : null,
            tooltip: 'Previous page',
          ),

          const SizedBox(width: 8),

          // Show loader instead of page numbers when loading
          if (isLoading)
            const SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          else
            ..._buildPageNumbers(context),

          const SizedBox(width: 8),

          // Next button
          _buildNavigationButton(
            context,
            icon: Icons.chevron_right,
            onPressed: currentPage < totalPages && !isLoading
                ? () => onPageChanged(currentPage + 1)
                : null,
            tooltip: 'Next page',
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationButton(
    BuildContext context, {
    required IconData icon,
    required VoidCallback? onPressed,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: onPressed,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: onPressed != null
                    ? primaryColor.withValues(alpha: 0.3)
                    : Colors.grey.withValues(alpha: 0.3),
              ),
              color: onPressed != null
                  ? Colors.transparent
                  : Colors.grey.withValues(alpha: 0.1),
            ),
            child: Icon(
              icon,
              size: 20,
              color: onPressed != null ? primaryColor : Colors.grey,
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildPageNumbers(BuildContext context) {
    List<Widget> pages = [];

    // Calculate which pages to show
    int startPage = 1;
    int endPage = totalPages;

    if (totalPages > 7) {
      if (currentPage <= 4) {
        endPage = 7;
      } else if (currentPage >= totalPages - 3) {
        startPage = totalPages - 6;
      } else {
        startPage = currentPage - 3;
        endPage = currentPage + 3;
      }
    }

    // Add first page and ellipsis if needed
    if (startPage > 1) {
      pages.add(_buildPageButton(context, 1));
      if (startPage > 2) {
        pages.add(_buildEllipsis());
      }
    }

    // Add page numbers
    for (int i = startPage; i <= endPage; i++) {
      pages.add(_buildPageButton(context, i));
    }

    // Add ellipsis and last page if needed
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.add(_buildEllipsis());
      }
      pages.add(_buildPageButton(context, totalPages));
    }

    return pages;
  }

  Widget _buildPageButton(BuildContext context, int page) {
    final isCurrentPage = page == currentPage;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 2),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: isLoading ? null : () => onPageChanged(page),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: isCurrentPage ? primaryColor : Colors.transparent,
              border: Border.all(
                color: isCurrentPage
                    ? primaryColor
                    : Colors.grey.withValues(alpha: 0.3),
              ),
            ),
            child: Text(
              page.toString(),
              style: TextStyle(
                color: isCurrentPage ? Colors.white : primaryColor,
                fontWeight: isCurrentPage ? FontWeight.w600 : FontWeight.w400,
                fontSize: 14,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEllipsis() {
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 4),
      child: Text(
        '...',
        style: TextStyle(
          color: Colors.grey,
          fontSize: 14,
        ),
      ),
    );
  }
}
