import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';

class SelectedImage {
  final String name;
  final String? extention;
  final Uint8List uInt8List;

  SelectedImage(
      {required this.name, required this.uInt8List, required this.extention});
}

class ImagePickerService {
  Future<SelectedImage?> pickImageAndCrop(BuildContext context) async {
    try {
      final result = await FilePicker.platform.pickFiles();
      if (result != null) {
        if (kIsWeb) {
          Uint8List? finalBytes =
              await imageCompressor(result.files.first.bytes!);
          return SelectedImage(
              name: result.files.first.name,
              extention: result.files.first.extension!,
              uInt8List: finalBytes);
        } else {
          // For non-web platforms, use the file bytes directly without cropping
          // to avoid image_cropper compatibility issues
          Uint8List? finalBytes =
              await imageCompressor(result.files.first.bytes!);
          return SelectedImage(
              name: result.files.first.name,
              extention: result.files.first.extension!,
              uInt8List: finalBytes);
        }
      }

      return null;
    } on Exception catch (e) {
      debugPrint(e.toString());
      return null;
    }
  }
}

Future<Uint8List> imageCompressor(Uint8List list) async {
  var result = await FlutterImageCompress.compressWithList(
    list,
    minHeight: 1920,
    minWidth: 1080,
    quality: 70,
    rotate: 135,
  );
  return result;
}
