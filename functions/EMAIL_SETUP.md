# WellFed Email Setup Guide

This guide explains how to set up and use the email functionality for sending welcome emails to new users.

## Prerequisites

1. **Install nodemailer** (if not already installed):
   ```bash
   cd functions
   npm install nodemailer
   ```

2. **Email Service Setup** - You'll need to configure an email service. This guide uses Gmail as an example.

## Gmail Setup (Recommended)

### Step 1: Enable 2-Factor Authentication
1. Go to your Google Account settings
2. Navigate to Security
3. Enable 2-Step Verification

### Step 2: Generate App Password
1. In Google Account Security settings
2. Go to "App passwords"
3. Generate a new app password for "Mail"
4. Copy the generated password (16 characters)

### Step 3: Update Configuration
In `functions/index.js`, update the transporter configuration:

```javascript
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: '<EMAIL>', // Replace with your Gmail address
    pass: 'your-app-password'     // Replace with the 16-character app password
  }
});
```

## Environment Variables (Recommended for Production)

For security, use environment variables instead of hardcoding credentials:

### Step 1: Set Environment Variables
```bash
firebase functions:config:set email.user="<EMAIL>"
firebase functions:config:set email.pass="your-app-password"
```

### Step 2: Update Code
```javascript
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: functions.config().email.user,
    pass: functions.config().email.pass
  }
});
```

## Testing the Email Functionality

### Method 1: Using the Test Script
```bash
cd functions
node test-email.js
```

Make sure to update the test email address in `test-email.js` before running.

### Method 2: Using Firebase Functions Emulator
```bash
cd functions
npm run serve
```

Then call the `sendWelcomeEmail` function from your frontend or using a tool like Postman.

## Usage

### Automatic Email on User Creation
The `createUser` function now automatically sends a welcome email when a new user is created. You can control this behavior:

```javascript
// Send email (default behavior)
const result = await createUser({
  email: '<EMAIL>',
  name: 'John Doe',
  // ... other fields
});

// Skip sending email
const result = await createUser({
  email: '<EMAIL>',
  name: 'John Doe',
  sendEmail: false, // This will skip the email
  // ... other fields
});
```

### Manual Email Sending
You can also call the `sendWelcomeEmail` function directly:

```javascript
const result = await sendWelcomeEmail({
  email: '<EMAIL>',
  name: 'John Doe',
  password: 'temporary-password'
});
```

## Email Template Customization

The email template includes both text and HTML versions. You can customize:

1. **Sender Information**: Update the `from` field
2. **Subject Line**: Modify the `subject` field
3. **Content**: Edit the text and HTML content
4. **Styling**: Modify the HTML template's CSS

## Troubleshooting

### Common Issues

1. **Authentication Error**: 
   - Ensure 2FA is enabled on your Google account
   - Use an app password, not your regular password
   - Check that the email and password are correct

2. **Permission Errors**:
   - Make sure the Firebase function has proper permissions
   - Check that the user calling the function is authenticated

3. **Email Not Received**:
   - Check spam/junk folders
   - Verify the recipient email address is correct
   - Check the email service logs

### Debug Mode
Add logging to see detailed error information:

```javascript
console.log('Email configuration:', {
  user: functions.config().email.user,
  // Don't log the password for security
});
```

## Alternative Email Services

You can use other email services by updating the transporter configuration:

### SendGrid
```javascript
const transporter = nodemailer.createTransport({
  service: 'SendGrid',
  auth: {
    user: 'apikey',
    pass: 'your-sendgrid-api-key'
  }
});
```

### Outlook/Hotmail
```javascript
const transporter = nodemailer.createTransport({
  service: 'hotmail',
  auth: {
    user: '<EMAIL>',
    pass: 'your-password'
  }
});
```

### Custom SMTP
```javascript
const transporter = nodemailer.createTransport({
  host: 'smtp.your-provider.com',
  port: 587,
  secure: false,
  auth: {
    user: '<EMAIL>',
    pass: 'your-password'
  }
});
```

## Security Best Practices

1. **Never commit credentials** to version control
2. **Use environment variables** for sensitive data
3. **Regularly rotate** email passwords/API keys
4. **Monitor email usage** to detect abuse
5. **Implement rate limiting** to prevent spam

## Support

If you encounter issues:
1. Check the Firebase Functions logs
2. Verify your email service configuration
3. Test with the provided test script
4. Review the troubleshooting section above
