/**
 * Test script for email functionality
 * Run this with: node test-email.js
 */

const nodemailer = require('nodemailer');

// Configure nodemailer transporter for WellFed (same as in index.js)
const transporter = nodemailer.createTransport({
  service: 'gmail', // You can change this to your preferred email service
  auth: {
    user: '<EMAIL>', // Replace with your email
    pass: 'your-app-password' // Replace with your app password or use environment variables
  }
});

// Test function to send welcome email
async function testWelcomeEmail() {
  try {
    const testEmail = '<EMAIL>'; // Replace with a test email
    const testName = 'Test User';
    const testPassword = 'temp123';

    const mailOptions = {
      from: '"WellFed Team" <<EMAIL>>',
      to: testEmail,
      subject: 'Welcome to WellFed – Here are your account details',
      text: `Hello ${testName.toUpperCase()},

Your WellFed account is ready.

Login: https://wellfed-9384f.web.app/
Username: ${testEmail}
Password: ${testPassword}

Please update your password after your first login.

– WellFed Team`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #2c3e50;">Welcome to WellFed!</h2>
          <p>Hello <strong>${testName.toUpperCase()}</strong>,</p>
          
          <p>Your WellFed account is ready and waiting for you.</p>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="color: #495057; margin-top: 0;">Your Account Details:</h3>
            <p><strong>Login URL:</strong> <a href="https://wellfed-9384f.web.app/" style="color: #007bff;">https://wellfed-9384f.web.app/</a></p>
            <p><strong>Username:</strong> ${testEmail}</p>
            <p><strong>Password:</strong> ${testPassword}</p>
          </div>
          
          <p style="color: #dc3545;"><strong>Important:</strong> Please update your password after your first login for security purposes.</p>
          
          <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
          
          <p>Best regards,<br>
          <strong>WellFed Team</strong></p>
        </div>
      `
    };

    console.log('Sending test email...');
    const info = await transporter.sendMail(mailOptions);
    console.log('✅ Test email sent successfully!');
    console.log('Message ID:', info.messageId);
    console.log('Preview URL:', nodemailer.getTestMessageUrl(info));
    
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('❌ Error sending test email:', error);
    return { success: false, error: error.message };
  }
}

// Run the test
if (require.main === module) {
  console.log('🧪 Testing WellFed email functionality...');
  console.log('📧 Make sure to update the email credentials in the transporter configuration!');
  console.log('');
  
  testWelcomeEmail()
    .then(result => {
      if (result.success) {
        console.log('');
        console.log('🎉 Email test completed successfully!');
        process.exit(0);
      } else {
        console.log('');
        console.log('💥 Email test failed:', result.error);
        process.exit(1);
      }
    })
    .catch(error => {
      console.error('💥 Unexpected error:', error);
      process.exit(1);
    });
}

module.exports = { testWelcomeEmail };
