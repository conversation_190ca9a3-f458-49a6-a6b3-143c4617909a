{"name": "functions", "description": "Cloud Functions for Firebase", "scripts": {"serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "index.js", "dependencies": {"date-and-time": "^3.0.2", "firebase-admin": "^11.8.0", "firebase-functions": "^4.3.1", "nodemailer": "^6.10.1", "stripe": "^12.12.0"}, "devDependencies": {"firebase-functions-test": "^3.1.0"}, "private": true}